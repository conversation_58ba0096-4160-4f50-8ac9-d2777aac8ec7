{% extends "base.html" %}

{% block title %}拖拽排表 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<style>
    .drag-board {
        margin: 20px 0;
    }

    .member-pool {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .member-card {
        color: white;
        padding: 12px;
        margin: 5px;
        border-radius: 8px;
        cursor: move;
        display: inline-block;
        min-width: 120px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        user-select: none;
        font-weight: bold;
        position: relative;
    }

    .member-card .edit-btn {
        position: absolute;
        top: 2px;
        right: 2px;
        background: rgba(255,255,255,0.2);
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 10px;
        color: white;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .member-card:hover .edit-btn {
        opacity: 1;
    }

    .member-card .edit-btn:hover {
        background: rgba(255,255,255,0.4);
    }

    /* 职责标识 */
    .member-card .position-badge {
        position: absolute;
        bottom: 2px;
        right: 2px;
        background: rgba(0,123,255,0.8);
        color: white;
        font-size: 8px;
        padding: 1px 4px;
        border-radius: 3px;
        font-weight: bold;
        z-index: 2;
    }

    /* 技能标签容器 - 改进版 */
    .member-card .skills-container {
        position: absolute;
        bottom: 2px;
        left: 2px;
        right: 45px;
        max-height: 24px;
        overflow: hidden;
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
        z-index: 1;
    }

    /* 技能标签 - 改进版 */
    .skill-tag {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        font-size: 9px;
        padding: 2px 5px;
        border-radius: 4px;
        font-weight: 600;
        white-space: nowrap;
        max-width: 70px;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.3;
        text-shadow: 0 1px 1px rgba(0,0,0,0.3);
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        border: 1px solid rgba(255,255,255,0.3);
    }

    /* 技能标签分类颜色 - 改进版 */
    .skill-tag.attack {
        background: linear-gradient(135deg, #FF5722 0%, #D32F2F 100%);
    }
    .skill-tag.defense {
        background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    }
    .skill-tag.support {
        background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    }
    .skill-tag.control {
        background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
    }
    .skill-tag.demolition {
        background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    }

    /* 小队折叠功能 */
    .squad-title {
        cursor: pointer;
        user-select: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 10px;
        background: rgba(255,255,255,0.1);
        border-radius: 5px;
        margin-bottom: 5px;
        transition: background 0.3s ease;
    }

    .squad-title:hover {
        background: rgba(255,255,255,0.2);
    }

    .member-count {
        font-size: 12px;
        color: rgba(255,255,255,0.8);
    }

    .collapse-icon {
        font-size: 12px;
        transition: transform 0.3s ease;
    }

    /* 职责编辑模态框 */
    .position-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.7);
        align-items: center;
        justify-content: center;
    }

    .position-modal-content {
        background-color: white;
        margin: auto;
        padding: 30px;
        border-radius: 15px;
        width: 90vw;
        max-width: 1200px;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        position: relative;
    }

    .position-option {
        display: block;
        width: 100%;
        padding: 10px;
        margin: 5px 0;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-weight: bold;
    }

    .position-option:hover {
        border-color: #667eea;
        background: #f8f9ff;
        transform: translateY(-2px);
    }

    .position-option.selected {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }

    /* 职业颜色区分 */
    .member-card[data-profession="素问"] { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
    .member-card[data-profession="铁衣"] { background: linear-gradient(135deg, #ff9800, #f57c00); }
    .member-card[data-profession="潮光"] { background: linear-gradient(135deg, #03a9f4, #0288d1); }
    .member-card[data-profession="九灵"] { background: linear-gradient(135deg, #a8e6cf, #dcedc1); color: #333; }
    .member-card[data-profession="龙吟"] { background: linear-gradient(135deg, #ffd93d, #6bcf7f); color: #333; }
    .member-card[data-profession="血河"] { background: linear-gradient(135deg, #667eea, #764ba2); }
    .member-card[data-profession="碎梦"] { background: linear-gradient(135deg, #00695c, #004d40); }
    .member-card[data-profession="玄机"] { background: linear-gradient(135deg, #ffeb3b, #fbc02d); color: #333; }
    .member-card[data-profession="神相"] { background: linear-gradient(135deg, #3f51b5, #303f9f); }
    .member-card[data-profession="沧澜"] { background: linear-gradient(135deg, #8bc34a, #689f38); color: #333; }
        
    .member-card:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.25);
        z-index: 10;
    }

    .member-card.dragging {
        opacity: 0.8;
        transform: rotate(5deg) scale(1.1);
        z-index: 1000;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }



    .organization-board {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 8px;
        margin-top: 15px;
    }

    .team-column {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        padding: 8px;
        min-height: 600px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .team-header {
        color: white;
        padding: 12px;
        margin-bottom: 10px;
        font-size: 1.2em;
        font-weight: bold;
        border-radius: 10px;
        position: relative;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .team-header .team-title {
        flex: 1;
        text-align: center;
    }

    .team-header .team-actions {
        display: flex;
        gap: 2px;
        margin-left: auto;
        flex-shrink: 0;
    }

    .team-header.attack {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    }
    .team-header.defense {
        background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%);
    }
    .team-header.special {
        background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    }

    .squad-section {
        margin-bottom: 8px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 8px;
        min-height: 110px;
        border: 2px solid rgba(0,0,0,0.05);
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .squad-section:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    }

    .squad-title {
        font-size: 0.8em;
        font-weight: bold;
        color: #495057;
        margin-bottom: 3px;
        padding: 2px 5px;
        background: #e9ecef;
        border-radius: 3px;
        text-align: center;
    }

    .squad-members {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        min-height: 80px;
        padding: 4px;
        border-radius: 6px;
        background: rgba(255,255,255,0.5);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    /* 折叠状态样式 */
    .squad-members.collapsed {
        min-height: 0 !important;
        max-height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        height: 0 !important;
        background: transparent !important;
        opacity: 0 !important;
        transform: scaleY(0) !important;
        transform-origin: top !important;
    }

    /* 折叠时小队容器也要收缩 */
    .squad-section.collapsed {
        min-height: 35px !important;
        padding-bottom: 5px !important;
    }

    .member-card {
        width: 65px;
        height: 40px;
        padding: 4px 6px;
        font-size: 0.75em;
        border-radius: 8px;
        cursor: move;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        line-height: 1.2;
        font-weight: 600;
        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border: 2px solid rgba(255,255,255,0.3);
        position: relative;
    }

    .member-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
        border-radius: 6px;
        pointer-events: none;
    }

    /* 特殊区域样式 */
    .special-area {
        background: #f8f9fa;
        border-radius: 5px;
        padding: 8px;
        margin-bottom: 8px;
        min-height: 120px;
        border: 1px dashed #6c757d;
    }

    .special-title {
        font-size: 0.9em;
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
        text-align: center;
        padding: 3px;
        background: #e9ecef;
        border-radius: 3px;
    }

    /* 统一的按钮基础样式 */
    .team-action-btn {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        backdrop-filter: blur(4px);
        min-width: 20px;
        height: 20px;
        line-height: 1;
    }

    .team-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 1);
    }

    /* 添加按钮样式 */
    .add-team-btn {
        color: #059669;
        border-color: rgba(5, 150, 105, 0.2);
    }

    .add-team-btn:hover {
        color: #047857;
        border-color: rgba(5, 150, 105, 0.4);
        background: rgba(236, 253, 245, 0.95);
    }

    /* 删除按钮样式 */
    .delete-team-btn {
        color: #dc2626;
        border-color: rgba(220, 38, 38, 0.2);
    }

    .delete-team-btn:hover {
        color: #b91c1c;
        border-color: rgba(220, 38, 38, 0.4);
        background: rgba(254, 242, 242, 0.95);
    }

    /* 统一所有团队头部的按钮样式 - 移除特殊样式，保持一致 */
    .team-header .team-action-btn {
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .team-header .add-team-btn:hover {
        background: rgba(236, 253, 245, 0.95);
        color: #047857;
    }

    .team-header .delete-team-btn:hover {
        background: rgba(254, 242, 242, 0.95);
        color: #b91c1c;
    }

    /* 可编辑团队名称样式 */
    .team-name-editable {
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 3px;
        transition: all 0.3s ease;
        position: relative;
    }

    .team-name-editable:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }

    .team-name-editable:hover::after {
        content: "双击编辑";
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        white-space: nowrap;
        z-index: 1000;
    }
        
        .sub-team-container.drag-over {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .sub-team-header {
            background: #6c757d;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            text-align: center;
            font-weight: bold;
        }

        /* 特殊头部样式 */
        .leave-header {
            background: #6c757d !important;
            color: white !important;
        }

        .substitute-header {
            background: #ffc107 !important;
            color: #333 !important;
        }
        
        .squads-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .squad-container {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            min-height: 60px;
        }
        
        .squad-section.drag-over, .special-area.drag-over {
            border-color: #007bff !important;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(0,123,255,0.3) !important;
        }
        
        .squad-header {
            font-size: 0.9em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 3px;
        }
        
        .squad-members {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        /* 拖拽板样式 */
        .drag-board {
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 新建成员按钮样式 */
        .new-member-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
        }

        .new-member-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(40, 167, 69, 0.4);
        }

        /* 职业颜色 - 渐变美化 */
        .member-card[data-profession="素问"] {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #333;
            text-shadow: 0 1px 2px rgba(255,255,255,0.5);
        }
        .member-card[data-profession="九灵"] {
            background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
            color: #333;
            text-shadow: 0 1px 2px rgba(255,255,255,0.5);
        }
        .member-card[data-profession="潮光"] {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .member-card[data-profession="血河"] {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .member-card[data-profession="神相"] {
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .member-card[data-profession="玄机"] {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: #333;
            text-shadow: 0 1px 2px rgba(255,255,255,0.5);
        }
        .member-card[data-profession="铁衣"] {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            color: #333;
            text-shadow: 0 1px 2px rgba(255,255,255,0.5);
        }
        .member-card[data-profession="龙吟"] {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .member-card[data-profession="碎梦"] {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        /* 特殊小队样式 */
        .special-squad {
            min-height: 80px;
        }

        .special-squad .squad-header {
            display: none;
        }

        /* 通知动画 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
</style>

<div class="drag-board">
            <!-- 页面标题和新建主分组按钮 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding: 0 10px;">
                <h2 style="color: #333; margin: 0;">🏰 组织架构拖拽排表</h2>
                <button onclick="showCreateMainGroupDialog()"
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;
                               padding: 10px 20px; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: bold;
                               transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);">
                    ➕ 新建主分组
                </button>
            </div>

            <!-- 组织架构拖拽区域 -->
            <div class="organization-board">
                <!-- 一团 -->
                <div class="team-column">
                    <div class="team-header attack">
                        <div class="team-title">
                            <span class="team-name-editable" ondblclick="editTeamName('进攻团', '一团', this)">一团</span> (进攻)
                        </div>
                        <div class="team-actions">
                            <button class="team-action-btn add-team-btn" onclick="addNewTeam('进攻团')" title="添加新团">+</button>
                            <button class="team-action-btn delete-team-btn" onclick="deleteTeam('进攻团', '一团')" title="删除团队">×</button>
                        </div>
                    </div>
                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                        <div class="squad-section"
                             data-main-group="进攻团"
                             data-sub-team="一团"
                             data-squad="{{ squad }}">
                            <div class="squad-title" onclick="toggleSquad('进攻团-一团-{{ squad }}')">
                                {{ squad }} <span class="member-count" id="count-进攻团-一团-{{ squad }}">现有0人</span>
                                <span class="collapse-icon" id="icon-进攻团-一团-{{ squad }}">▼</span>
                            </div>
                            <div class="squad-members" id="squad-进攻团-一团-{{ squad }}"
                                 data-main-group="进攻团"
                                 data-sub-team="一团"
                                 data-squad="{{ squad }}">
                                <!-- 显示分配到此小队的成员 -->
                                {% for member in members %}
                                    {% set main_group = member.get('main_group', '其他团') %}
                                    {% set sub_team = member.get('sub_team', '一团') %}
                                    {% set member_squad = member.get('squad', '1队') %}
                                    {% if main_group == '进攻团' and sub_team == '一团' and member_squad == squad %}
                                        <div class="member-card"
                                             draggable="true"
                                             data-member-name="{{ member.name }}"
                                             data-profession="{{ member.profession }}"
                                             data-position="{{ member.get('position', '拆塔') }}"
                                             data-current-main-group="{{ main_group }}"
                                             data-current-sub-team="{{ sub_team }}"
                                             data-current-squad="{{ member_squad }}"
                                             data-skills="{{ member.get('skills', [])|join(',') }}">
                                            {{ member.name }}
                                            <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                background: none;
                                                border: none;
                                                font-size: 14px;
                                                cursor: pointer;
                                                padding: 2px 4px;
                                                border-radius: 3px;
                                                color: #666;
                                                transition: all 0.2s ease;
                                            " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <div class="skills-container">
                                                {% for skill in member.get('skills', []) %}
                                                    <span class="skill-tag">{{ skill }}</span>
                                                {% endfor %}
                                            </div>
                                            <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- 二团 -->
                <div class="team-column">
                    <div class="team-header attack">
                        <div class="team-title">
                            <span class="team-name-editable" ondblclick="editTeamName('进攻团', '二团', this)">二团</span> (进攻)
                        </div>
                        <div class="team-actions">
                            <button class="team-action-btn delete-team-btn" onclick="deleteTeam('进攻团', '二团')" title="删除团队">×</button>
                        </div>
                    </div>
                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                        <div class="squad-section"
                             data-main-group="进攻团"
                             data-sub-team="二团"
                             data-squad="{{ squad }}">
                            <div class="squad-title" onclick="toggleSquad('进攻团-二团-{{ squad }}')">
                                {{ squad }} <span class="member-count" id="count-进攻团-二团-{{ squad }}">现有0人</span>
                                <span class="collapse-icon" id="icon-进攻团-二团-{{ squad }}">▼</span>
                            </div>
                            <div class="squad-members" id="squad-进攻团-二团-{{ squad }}"
                                 data-main-group="进攻团"
                                 data-sub-team="二团"
                                 data-squad="{{ squad }}">
                                <!-- 显示分配到此小队的成员 -->
                                {% for member in members %}
                                    {% set main_group = member.get('main_group', '其他团') %}
                                    {% set sub_team = member.get('sub_team', '二团') %}
                                    {% set member_squad = member.get('squad', '1队') %}
                                    {% if main_group == '进攻团' and sub_team == '二团' and member_squad == squad %}
                                        <div class="member-card"
                                             draggable="true"
                                             data-member-name="{{ member.name }}"
                                             data-profession="{{ member.profession }}"
                                             data-position="{{ member.get('position', '拆塔') }}"
                                             data-current-main-group="{{ main_group }}"
                                             data-current-sub-team="{{ sub_team }}"
                                             data-current-squad="{{ member_squad }}"
                                             data-skills="{{ member.get('skills', [])|join(',') }}">
                                            {{ member.name }}
                                            <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                background: none;
                                                border: none;
                                                font-size: 14px;
                                                cursor: pointer;
                                                padding: 2px 4px;
                                                border-radius: 3px;
                                                color: #666;
                                                transition: all 0.2s ease;
                                            " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <div class="skills-container">
                                                {% for skill in member.get('skills', []) %}
                                                    <span class="skill-tag">{{ skill }}</span>
                                                {% endfor %}
                                            </div>
                                            <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- 三团 -->
                <div class="team-column">
                    <div class="team-header attack">
                        <div class="team-title">
                            <span class="team-name-editable" ondblclick="editTeamName('进攻团', '三团', this)">三团</span> (进攻)
                        </div>
                        <div class="team-actions">
                            <button class="team-action-btn delete-team-btn" onclick="deleteTeam('进攻团', '三团')" title="删除团队">×</button>
                        </div>
                    </div>
                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                        <div class="squad-section"
                             data-main-group="进攻团"
                             data-sub-team="三团"
                             data-squad="{{ squad }}">
                            <div class="squad-title" onclick="toggleSquad('进攻团-三团-{{ squad }}')">
                                {{ squad }} <span class="member-count" id="count-进攻团-三团-{{ squad }}">现有0人</span>
                                <span class="collapse-icon" id="icon-进攻团-三团-{{ squad }}">▼</span>
                            </div>
                            <div class="squad-members" id="squad-进攻团-三团-{{ squad }}"
                                 data-main-group="进攻团"
                                 data-sub-team="三团"
                                 data-squad="{{ squad }}">
                                <!-- 显示分配到此小队的成员 -->
                                {% for member in members %}
                                    {% set main_group = member.get('main_group', '其他团') %}
                                    {% set sub_team = member.get('sub_team', '三团') %}
                                    {% set member_squad = member.get('squad', '1队') %}
                                    {% if main_group == '进攻团' and sub_team == '三团' and member_squad == squad %}
                                        <div class="member-card"
                                             draggable="true"
                                             data-member-name="{{ member.name }}"
                                             data-profession="{{ member.profession }}"
                                             data-position="{{ member.get('position', '拆塔') }}"
                                             data-current-main-group="{{ main_group }}"
                                             data-current-sub-team="{{ sub_team }}"
                                             data-current-squad="{{ member_squad }}"
                                             data-skills="{{ member.get('skills', [])|join(',') }}">
                                            {{ member.name }}
                                            <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                background: none;
                                                border: none;
                                                font-size: 14px;
                                                cursor: pointer;
                                                padding: 2px 4px;
                                                border-radius: 3px;
                                                color: #666;
                                                transition: all 0.2s ease;
                                            " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <div class="skills-container">
                                                {% for skill in member.get('skills', []) %}
                                                    <span class="skill-tag">{{ skill }}</span>
                                                {% endfor %}
                                            </div>
                                            <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- 动态创建的进攻团已合并到自定义主分组部分 -->

                <!-- 防守团 -->
                <div class="team-column">
                    <div class="team-header defense">
                        <div class="team-title">
                            <span class="team-name-editable" ondblclick="editTeamName('防守团', '防守团', this)">一团</span> (防守)
                        </div>
                        <div class="team-actions">
                            <button class="team-action-btn add-team-btn" onclick="addNewTeam('防守团')" title="添加防守二团">+</button>
                            <button class="team-action-btn delete-team-btn" onclick="deleteTeam('防守团', '防守团')" title="删除团队">×</button>
                        </div>
                    </div>
                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                        <div class="squad-section"
                             data-main-group="防守团"
                             data-sub-team="防守团"
                             data-squad="{{ squad }}">
                            <div class="squad-title" onclick="toggleSquad('防守团-防守团-{{ squad }}')">
                                {{ squad }} <span class="member-count" id="count-防守团-防守团-{{ squad }}">现有0人</span>
                                <span class="collapse-icon" id="icon-防守团-防守团-{{ squad }}">▼</span>
                            </div>
                            <div class="squad-members" id="squad-防守团-防守团-{{ squad }}"
                                 data-main-group="防守团"
                                 data-sub-team="防守团"
                                 data-squad="{{ squad }}">
                                <!-- 显示分配到此小队的成员 -->
                                {% for member in members %}
                                    {% set main_group = member.get('main_group', '其他团') %}
                                    {% set sub_team = member.get('sub_team', '防守团') %}
                                    {% set member_squad = member.get('squad', '1队') %}
                                    {% if main_group == '防守团' and sub_team == '防守团' and member_squad == squad %}
                                        <div class="member-card"
                                             draggable="true"
                                             data-member-name="{{ member.name }}"
                                             data-profession="{{ member.profession }}"
                                             data-position="{{ member.get('position', '拆塔') }}"
                                             data-current-main-group="{{ main_group }}"
                                             data-current-sub-team="{{ sub_team }}"
                                             data-current-squad="{{ member_squad }}"
                                             data-skills="{{ member.get('skills', [])|join(',') }}">
                                            {{ member.name }}
                                            <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                background: none;
                                                border: none;
                                                font-size: 14px;
                                                cursor: pointer;
                                                padding: 2px 4px;
                                                border-radius: 3px;
                                                color: #666;
                                                transition: all 0.2s ease;
                                            " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <div class="skills-container">
                                                {% for skill in member.get('skills', []) %}
                                                    <span class="skill-tag">{{ skill }}</span>
                                                {% endfor %}
                                            </div>
                                            <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- 动态创建的防守团已合并到自定义主分组部分 -->

                <!-- 自定义主分组和子团队 -->
                {% for main_group in all_main_groups %}
                    {% if main_group not in ['进攻团', '防守团', '其他团'] %}
                        <!-- 获取该主分组的所有子团队 -->
                        {% set main_group_teams = [] %}
                        {% for config in all_team_configs %}
                            {% if config.main_group == main_group %}
                                {% set _ = main_group_teams.append(config) %}
                            {% endif %}
                        {% endfor %}

                        <!-- 显示主分组本身（如果没有其他子团队，或者作为第一个） -->
                        {% set has_main_as_sub = main_group_teams|selectattr('sub_team', 'equalto', main_group)|list|length > 0 %}
                        {% if not main_group_teams or has_main_as_sub %}
                            <div class="team-column">
                                <div class="team-header custom-main-group" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <div class="team-title">
                                        <span class="team-name-editable" ondblclick="editTeamName('{{ main_group }}', '{{ main_group }}', this)">{{ main_group }}</span>
                                    </div>
                                    <div class="team-actions">
                                        <button class="team-action-btn add-team-btn" onclick="addNewTeam('{{ main_group }}')" title="添加子团队">+</button>
                                        <button class="team-action-btn delete-team-btn" onclick="deleteMainGroup('{{ main_group }}')" title="删除主分组">×</button>
                                    </div>
                                </div>
                                {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                    <div class="squad-section" data-main-group="{{ main_group }}" data-sub-team="{{ main_group }}" data-squad="{{ squad }}">
                                        <div class="squad-title" onclick="toggleSquad('{{ main_group }}-{{ main_group }}-{{ squad }}')">
                                            {{ squad }} <span class="member-count" id="count-{{ main_group }}-{{ main_group }}-{{ squad }}">现有0人</span>
                                            <span class="collapse-icon" id="icon-{{ main_group }}-{{ main_group }}-{{ squad }}">▼</span>
                                        </div>
                                        <div class="squad-members" id="squad-{{ main_group }}-{{ main_group }}-{{ squad }}"
                                             data-main-group="{{ main_group }}"
                                             data-sub-team="{{ main_group }}"
                                             data-squad="{{ squad }}">
                                            <!-- 显示分配到此小队的成员 -->
                                            {% for member in members %}
                                                {% set member_main_group = member.get('main_group', '其他团') %}
                                                {% set member_sub_team = member.get('sub_team', '一团') %}
                                                {% set member_squad = member.get('squad', '1队') %}
                                                {% if member_main_group == main_group and member_sub_team == main_group and member_squad == squad %}
                                                    <div class="member-card"
                                                         draggable="true"
                                                         data-member-name="{{ member.name }}"
                                                         data-profession="{{ member.profession }}"
                                                         data-position="{{ member.get('position', '拆塔') }}"
                                                         data-current-main-group="{{ member_main_group }}"
                                                         data-current-sub-team="{{ member_sub_team }}"
                                                         data-current-squad="{{ member_squad }}"
                                                         data-skills="{{ member.get('skills', [])|join(',') }}">
                                                        {{ member.name }}
                                                        <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                            background: none;
                                                            border: none;
                                                            font-size: 14px;
                                                            cursor: pointer;
                                                            padding: 2px 4px;
                                                            border-radius: 3px;
                                                            color: #666;
                                                            transition: all 0.2s ease;
                                                        " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                        <div class="skills-container">
                                                            {% for skill in member.get('skills', []) %}
                                                                <span class="skill-tag">{{ skill }}</span>
                                                            {% endfor %}
                                                        </div>
                                                        <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- 显示该主分组的其他子团队 -->
                        {% for config in main_group_teams %}
                            {% if config.sub_team != main_group %}
                                <div class="team-column">
                                    <div class="team-header custom-main-group" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                        <div class="team-title">
                                            <span class="team-name-editable" ondblclick="editTeamName('{{ config.main_group }}', '{{ config.sub_team }}', this)">{{ config.sub_team }}</span>
                                        </div>
                                        <div class="team-actions">
                                            <button class="team-action-btn delete-team-btn" onclick="deleteTeam('{{ config.main_group }}', '{{ config.sub_team }}')" title="删除团队">×</button>
                                        </div>
                                    </div>
                                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                        <div class="squad-section" data-main-group="{{ config.main_group }}" data-sub-team="{{ config.sub_team }}" data-squad="{{ squad }}">
                                            <div class="squad-title" onclick="toggleSquad('{{ config.main_group }}-{{ config.sub_team }}-{{ squad }}')">
                                                {{ squad }} <span class="member-count" id="count-{{ config.main_group }}-{{ config.sub_team }}-{{ squad }}">现有0人</span>
                                                <span class="collapse-icon" id="icon-{{ config.main_group }}-{{ config.sub_team }}-{{ squad }}">▼</span>
                                            </div>
                                            <div class="squad-members" id="squad-{{ config.main_group }}-{{ config.sub_team }}-{{ squad }}"
                                                 data-main-group="{{ config.main_group }}"
                                                 data-sub-team="{{ config.sub_team }}"
                                                 data-squad="{{ squad }}">
                                                <!-- 显示分配到此小队的成员 -->
                                                {% for member in members %}
                                                    {% set member_main_group = member.get('main_group', '其他团') %}
                                                    {% set member_sub_team = member.get('sub_team', '一团') %}
                                                    {% set member_squad = member.get('squad', '1队') %}
                                                    {% if member_main_group == config.main_group and member_sub_team == config.sub_team and member_squad == squad %}
                                                        <div class="member-card"
                                                             draggable="true"
                                                             data-member-name="{{ member.name }}"
                                                             data-profession="{{ member.profession }}"
                                                             data-position="{{ member.get('position', '拆塔') }}"
                                                             data-current-main-group="{{ member_main_group }}"
                                                             data-current-sub-team="{{ member_sub_team }}"
                                                             data-current-squad="{{ member_squad }}"
                                                             data-skills="{{ member.get('skills', [])|join(',') }}">
                                                            {{ member.name }}
                                                            <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                                                background: none;
                                                                border: none;
                                                                font-size: 14px;
                                                                cursor: pointer;
                                                                padding: 2px 4px;
                                                                border-radius: 3px;
                                                                color: #666;
                                                                transition: all 0.2s ease;
                                                            " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                                                <i class="fas fa-cog"></i>
                                                            </button>
                                                            <div class="skills-container">
                                                                {% for skill in member.get('skills', []) %}
                                                                    <span class="skill-tag">{{ skill }}</span>
                                                                {% endfor %}
                                                            </div>
                                                            <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                                        </div>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}

                <!-- 请假和替补 -->
                <div class="team-column">
                    <div class="team-header special">其他</div>

                    <!-- 请假区域 -->
                    <div class="special-area">
                        <div class="special-title">
                            <i class="fas fa-calendar-times"></i> 请假 <span class="member-count" id="count-其他团-请假-请假">现有0人</span>
                        </div>
                        <div class="squad-members"
                             id="squad-其他团-请假-请假"
                             data-main-group="其他团"
                             data-sub-team="请假"
                             data-squad="请假">
                            <!-- 请假成员显示在这里 -->
                            {% for member in members %}
                                {% set main_group = member.get('main_group', '其他团') %}
                                {% set sub_team = member.get('sub_team', '') %}
                                {% set squad = member.get('squad', '') %}
                                {% set status = member.get('status', '主力') %}
                                {% if (main_group == '其他团' and (sub_team == '请假' or squad == '请假')) or status == '请假' %}
                                    <div class="member-card"
                                         draggable="true"
                                         data-member-name="{{ member.name }}"
                                         data-profession="{{ member.profession }}"
                                         data-position="{{ member.get('position', '拆塔') }}"
                                         data-current-main-group="{{ main_group }}"
                                         data-current-sub-team="{{ sub_team }}"
                                         data-current-squad="{{ squad }}"
                                         data-skills="{{ member.get('skills', [])|join(',') }}">
                                        {{ member.name }}
                                        <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                            background: none;
                                            border: none;
                                            font-size: 14px;
                                            cursor: pointer;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            color: #666;
                                            transition: all 0.2s ease;
                                        " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <div class="skills-container">
                                            {% for skill in member.get('skills', []) %}
                                                <span class="skill-tag">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                        <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 替补区域 -->
                    <div class="special-area">
                        <div class="special-title">
                            <i class="fas fa-user-plus"></i> 替补 <span class="member-count" id="count-其他团-替补-替补">现有0人</span>
                        </div>
                        <div class="squad-members"
                             id="squad-其他团-替补-替补"
                             data-main-group="其他团"
                             data-sub-team="替补"
                             data-squad="替补">
                            <!-- 替补成员显示在这里 -->
                            {% for member in members %}
                                {% set main_group = member.get('main_group') %}
                                {% set sub_team = member.get('sub_team') %}
                                {% set squad = member.get('squad') %}
                                {% set status = member.get('status', '主力') %}
                                {# 显示条件：明确设置为替补 OR 数据为空/null（默认归属替补） #}
                                {% if (sub_team == '替补' or squad == '替补') or status == '替补' or (main_group is none or sub_team is none or squad is none) %}
                                    <div class="member-card"
                                         draggable="true"
                                         data-member-name="{{ member.name }}"
                                         data-profession="{{ member.profession }}"
                                         data-position="{{ member.get('position', '拆塔') }}"
                                         data-current-main-group="{{ main_group }}"
                                         data-current-sub-team="{{ sub_team }}"
                                         data-current-squad="{{ squad }}"
                                         data-skills="{{ member.get('skills', [])|join(',') }}">
                                        {{ member.name }}
                                        <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                            background: none;
                                            border: none;
                                            font-size: 14px;
                                            cursor: pointer;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            color: #666;
                                            transition: all 0.2s ease;
                                        " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <div class="skills-container">
                                            {% for skill in member.get('skills', []) %}
                                                <span class="skill-tag">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                        <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 帮外成员区域 -->
                    <div class="special-area">
                        <div class="special-title" onclick="toggleSquad('其他团-帮外-帮外')" style="cursor: pointer;">
                            <i class="fas fa-users-slash"></i> 帮外成员
                            <span class="member-count" id="count-其他团-帮外-帮外">现有0人</span>
                            <span class="collapse-icon" id="icon-其他团-帮外-帮外">▶</span>
                        </div>
                        <div class="squad-members collapsed"
                             id="squad-其他团-帮外-帮外"
                             data-main-group="其他团"
                             data-sub-team="帮外"
                             data-squad="帮外"
                             style="display: none;">
                            <!-- 帮外成员显示在这里 -->
                            {% for member in members %}
                                {% set main_group = member.get('main_group', '其他团') %}
                                {% set sub_team = member.get('sub_team', '') %}
                                {% set squad = member.get('squad', '') %}
                                {% set status = member.get('status', '主力') %}
                                {% if (main_group == '其他团' and (sub_team == '帮外' or squad == '帮外')) or status == '帮外' %}
                                    <div class="member-card"
                                         draggable="true"
                                         data-member-name="{{ member.name }}"
                                         data-profession="{{ member.profession }}"
                                         data-position="{{ member.get('position', '拆塔') }}"
                                         data-current-main-group="{{ main_group }}"
                                         data-current-sub-team="{{ sub_team }}"
                                         data-current-squad="{{ squad }}"
                                         data-skills="{{ member.get('skills', [])|join(',') }}">
                                        {{ member.name }}
                                        <button class="edit-btn" onclick="showMemberEditMenu('{{ member.name }}', event)" title="编辑成员" style="
                                            background: none;
                                            border: none;
                                            font-size: 14px;
                                            cursor: pointer;
                                            padding: 2px 4px;
                                            border-radius: 3px;
                                            color: #666;
                                            transition: all 0.2s ease;
                                        " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <div class="skills-container">
                                            {% for skill in member.get('skills', []) %}
                                                <span class="skill-tag">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                        <span class="position-badge">{{ member.get('position', '拆塔') }}</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
    </div>

    <!-- 快速新建按钮 -->
    <button style="position: fixed; bottom: 30px; right: 30px; z-index: 1001; background: #28a745; color: white; padding: 15px; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);" onclick="showQuickCreateModal()">
        <i class="fas fa-plus-circle"></i> 快速新建
    </button>



    <!-- 新建成员模态框 -->
    <div id="newMemberModal" class="position-modal">
        <div class="position-modal-content" style="max-width: 500px;">
            <h4 style="text-align: center; margin-bottom: 20px;">
                <i class="fas fa-user-plus"></i> 新建成员
            </h4>

            <form id="newMemberForm">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">成员姓名 *</label>
                    <input type="text" id="newMemberName" required
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;"
                           placeholder="请输入成员姓名">
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">职业 *</label>
                    <select id="newMemberProfession" required
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                        <option value="">请选择职业</option>
                        <option value="素问">素问</option>
                        <option value="九灵">九灵</option>
                        <option value="潮光">潮光</option>
                        <option value="血河">血河</option>
                        <option value="神相">神相</option>
                        <option value="玄机">玄机</option>
                        <option value="铁衣">铁衣</option>
                        <option value="龙吟">龙吟</option>
                        <option value="碎梦">碎梦</option>
                        <option value="沧澜">沧澜</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">职责</label>
                    <select id="newMemberPosition"
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                        <option value="拆塔">拆塔</option>
                        <option value="击杀">击杀</option>
                        <option value="人伤">人伤</option>
                        <option value="治疗">治疗</option>
                        <option value="扛伤">扛伤</option>
                        <option value="辅助">辅助</option>
                    </select>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="button" onclick="closeNewMemberModal()"
                            style="padding: 10px 20px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        取消
                    </button>
                    <button type="submit" id="createMemberBtn"
                            style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-save"></i> 创建成员
                    </button>
                    <button type="button" onclick="testCreateMember()"
                            style="padding: 10px 20px; margin-left: 10px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        测试创建
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 快速新建成员模态框 -->
    <div id="quickCreateModal" class="position-modal">
        <div class="position-modal-content" style="max-width: 400px;">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">
                <i class="fas fa-user-plus"></i> 快速新建成员
            </h3>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">成员姓名 *</label>
                <input type="text" id="quickMemberName" placeholder="请输入成员姓名"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">职业 *</label>
                <select id="quickMemberProfession" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                    <option value="">请选择职业</option>
                    <option value="素问">素问</option>
                    <option value="九灵">九灵</option>
                    <option value="潮光">潮光</option>
                    <option value="血河">血河</option>
                    <option value="神相">神相</option>
                    <option value="玄机">玄机</option>
                    <option value="铁衣">铁衣</option>
                    <option value="龙吟">龙吟</option>
                    <option value="碎梦">碎梦</option>
                    <option value="沧澜">沧澜</option>
                </select>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">职责</label>
                <select id="quickMemberPosition" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                    <option value="拆塔">拆塔</option>
                    <option value="击杀">击杀</option>
                    <option value="人伤">人伤</option>
                    <option value="治疗">治疗</option>
                    <option value="扛伤">扛伤</option>
                    <option value="辅助">辅助</option>
                </select>
            </div>

            <div style="text-align: center;">
                <button onclick="closeQuickCreateModal()"
                        style="padding: 10px 20px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    取消
                </button>
                <button onclick="quickCreateMember()"
                        style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-save"></i> 创建成员
                </button>
            </div>
        </div>
    </div>

    <!-- 成员编辑菜单 -->
    <div id="memberEditMenu" class="position-modal" style="display: none;">
        <div class="position-modal-content" style="max-width: 500px;">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">
                <i class="fas fa-user-edit"></i> 编辑成员信息
            </h3>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">成员姓名 *</label>
                <input type="text" id="editMemberName"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">职业 *</label>
                <select id="editMemberProfession" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                    <option value="素问">素问</option>
                    <option value="九灵">九灵</option>
                    <option value="潮光">潮光</option>
                    <option value="血河">血河</option>
                    <option value="神相">神相</option>
                    <option value="玄机">玄机</option>
                    <option value="铁衣">铁衣</option>
                    <option value="龙吟">龙吟</option>
                    <option value="碎梦">碎梦</option>
                    <option value="沧澜">沧澜</option>
                </select>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px; font-weight: bold;">职责</label>
                <select id="editMemberPosition" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                    <option value="拆塔">拆塔</option>
                    <option value="击杀">击杀</option>
                    <option value="人伤">人伤</option>
                    <option value="治疗">治疗</option>
                    <option value="扛伤">扛伤</option>
                    <option value="辅助">辅助</option>
                </select>
            </div>

            <div style="text-align: center;">
                <button onclick="closeMemberEditMenu()"
                        style="padding: 10px 20px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    取消
                </button>
                <button onclick="saveMemberEdit()"
                        style="padding: 10px 20px; margin-right: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-save"></i> 保存修改
                </button>
                <button onclick="deleteMember()"
                        style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-trash"></i> 删除成员
                </button>
            </div>
        </div>
    </div>

    <!-- 职责编辑模态框 -->
    <div id="positionModal" class="position-modal">
        <div class="position-modal-content">
            <h4 style="text-align: center; margin-bottom: 20px;">
                <i class="fas fa-user-cog"></i> 设置成员职责
            </h4>
            <div style="text-align: center; margin-bottom: 15px;">
                <strong>成员：<span id="editingMemberName"></span></strong>
            </div>
            <div id="positionOptions">
                <button class="position-option" data-position="拆塔" onclick="selectPosition('拆塔')">
                    🏗️ 拆塔 - 专注建筑伤害
                </button>
                <button class="position-option" data-position="击杀" onclick="selectPosition('击杀')">
                    ⚔️ 击杀 - 专注击杀敌人
                </button>
                <button class="position-option" data-position="人伤" onclick="selectPosition('人伤')">
                    💥 人伤 - 专注玩家伤害
                </button>
                <button class="position-option" data-position="治疗" onclick="selectPosition('治疗')">
                    💚 治疗 - 专注治疗复活
                </button>
                <button class="position-option" data-position="扛伤" onclick="selectPosition('扛伤')">
                    🛡️ 扛伤 - 专注承受伤害
                </button>
                <button class="position-option" data-position="辅助" onclick="selectPosition('辅助')">
                    🤝 辅助 - 综合支援作用
                </button>
            </div>
            <div style="text-align: center; margin-top: 15px; color: #666; font-size: 14px;">
                点击职责直接确认，或点击外部取消
            </div>
            <div style="text-align: center; margin-top: 15px; border-top: 1px solid #eee; padding-top: 15px;">
                <button onclick="switchToFullEdit()"
                        style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                    <i class="fas fa-edit"></i> 编辑成员信息
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
        // 全局变量声明
        let draggedElement = null;
        let currentEditingMember = null;
        let currentEditingMemberName = null;
        
        // 为所有成员卡片添加拖拽事件
        document.querySelectorAll('.member-card').forEach(card => {
            card.addEventListener('dragstart', handleDragStart);
            card.addEventListener('dragend', handleDragEnd);
            card.addEventListener('dblclick', handleDoubleClick);
        });
        
        // 为所有小队容器添加拖放事件
        document.querySelectorAll('.squad-section, .special-area').forEach(container => {
            container.addEventListener('dragover', handleDragOver);
            container.addEventListener('drop', handleDrop);
            container.addEventListener('dragenter', handleDragEnter);
            container.addEventListener('dragleave', handleDragLeave);
        });

        // 为成员池添加拖放事件
        const memberPool = document.getElementById('member-pool-container');
        memberPool.addEventListener('dragover', handleDragOver);
        memberPool.addEventListener('drop', handleDropToPool);
        memberPool.addEventListener('dragenter', handleDragEnter);
        memberPool.addEventListener('dragleave', handleDragLeave);
        
        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
        }
        
        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
        }

        function handleDoubleClick(e) {
            e.preventDefault();
            const memberCard = this;
            const memberName = memberCard.dataset.memberName;
            const currentMainGroup = memberCard.dataset.currentMainGroup;
            const currentSubTeam = memberCard.dataset.currentSubTeam;
            const currentSquad = memberCard.dataset.currentSquad;

            // 检查当前是否已经是替补
            if (currentMainGroup === '其他团' && currentSubTeam === '替补' && currentSquad === '替补') {
                // 如果已经是替补，提示用户
                showUpdateNotification(memberName, '已经是替补状态');
                return;
            }

            // 确认对话框
            if (confirm(`确定将 ${memberName} 设为替补吗？`)) {
                // 移动到替补区域
                const substituteArea = document.getElementById('squad-其他团-替补-替补');
                if (substituteArea) {
                    substituteArea.appendChild(memberCard);

                    // 更新数据属性
                    memberCard.dataset.currentMainGroup = '其他团';
                    memberCard.dataset.currentSubTeam = '替补';
                    memberCard.dataset.currentSquad = '替补';

                    // 保存到服务器
                    updateMemberPosition(memberName, '其他团', '替补', '替补');

                    // 更新人数统计
                    updateSquadCounts();

                    // 显示成功提示
                    showUpdateNotification(memberName, '已设为替补');
                }
            }
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }
        
        function handleDragEnter(e) {
            this.classList.add('drag-over');
        }
        
        function handleDragLeave(e) {
            this.classList.remove('drag-over');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            console.log('🎯 handleDrop 被调用');
            console.log('🎯 this:', this);
            console.log('🎯 this.className:', this.className);
            console.log('🎯 this.dataset:', this.dataset);

            if (draggedElement) {
                console.log('🎯 draggedElement:', draggedElement);
                console.log('🎯 draggedElement.dataset:', draggedElement.dataset);

                // 获取目标小队成员容器
                const squadMembers = this.querySelector('.squad-members');
                console.log('🎯 squadMembers:', squadMembers);
                console.log('🎯 squadMembers.dataset:', squadMembers ? squadMembers.dataset : null);

                // 获取目标位置信息
                let mainGroup, subTeam, squad;

                if (squadMembers) {
                    // 如果找到了 .squad-members，优先从它获取数据
                    mainGroup = squadMembers.dataset.mainGroup;
                    subTeam = squadMembers.dataset.subTeam;
                    squad = squadMembers.dataset.squad;
                    console.log('🎯 从 squadMembers 获取数据:', { mainGroup, subTeam, squad });
                } else {
                    // 如果没有找到 .squad-members，从 this 获取数据
                    mainGroup = this.dataset.mainGroup;
                    subTeam = this.dataset.subTeam;
                    squad = this.dataset.squad;
                    console.log('🎯 从 this 获取数据:', { mainGroup, subTeam, squad });
                }

                // 如果仍然没有获取到数据，记录错误并返回
                if (!mainGroup || !subTeam || !squad) {
                    console.error('❌ 无法获取目标位置信息:', {
                        mainGroup, subTeam, squad,
                        thisDataset: this.dataset,
                        squadMembersDataset: squadMembers ? squadMembers.dataset : null,
                        thisHTML: this.outerHTML.substring(0, 200)
                    });
                    alert('拖拽失败：无法确定目标位置');
                    return;
                }

                console.log('✅ 成功获取目标位置:', { mainGroup, subTeam, squad });

                // 检查人数限制（每队最多6人，特殊区域不限制）
                if (squad !== '请假' && squad !== '替补' && squad !== '帮外') {
                    const currentMembers = squadMembers.children.length;
                    if (currentMembers >= 6) {
                        alert(`${squad}已满员（6人），无法添加更多成员！`);
                        return;
                    }
                }

                // 将成员卡片移动到目标小队
                squadMembers.appendChild(draggedElement);

                // 更新成员卡片的数据属性
                draggedElement.dataset.currentMainGroup = mainGroup;
                draggedElement.dataset.currentSubTeam = subTeam;
                draggedElement.dataset.currentSquad = squad;

                // 立即保存到服务器
                updateMemberPosition(
                    draggedElement.dataset.memberName,
                    mainGroup,
                    subTeam,
                    squad
                );

                // 更新人数统计
                updateSquadCounts();
            }
        }

        function handleDropToPool(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            if (draggedElement) {
                // 将成员移回替补区域（替代原来的成员池）
                const substituteArea = document.getElementById('squad-其他团-替补-替补');
                if (substituteArea) {
                    substituteArea.appendChild(draggedElement);
                }

                // 重置成员数据为替补
                draggedElement.dataset.currentMainGroup = '其他团';
                draggedElement.dataset.currentSubTeam = '替补';
                draggedElement.dataset.currentSquad = '替补';

                // 保存到服务器
                updateMemberPosition(
                    draggedElement.dataset.memberName,
                    '其他团',
                    '替补',
                    '替补'
                );
            }
        }

        function updateMemberPosition(memberName, mainGroup, subTeam, squad) {
            console.log('🔄 更新成员位置:', {
                memberName: memberName,
                mainGroup: mainGroup,
                subTeam: subTeam,
                squad: squad
            });

            // 添加调用栈信息
            console.log('📍 调用栈:', new Error().stack);

            // 检查参数是否有效
            if (!memberName) {
                console.error('❌ 成员名为空！');
                alert('错误：成员名为空');
                return;
            }

            if (!mainGroup || !subTeam || !squad) {
                console.error('❌ 位置参数无效！', {
                    mainGroup: mainGroup,
                    subTeam: subTeam,
                    squad: squad
                });
                alert('错误：位置参数无效，请重试');
                return;
            }

            fetch('/update_member_position', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    member_name: memberName,
                    main_group: mainGroup,
                    sub_team: subTeam,
                    squad: squad
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('位置更新成功:', memberName, mainGroup, subTeam, squad);

                    // 显示详细的更新信息
                    if (data.updated_fields && data.updated_fields.length > 0) {
                        const updatedFieldsText = data.updated_fields.join(', ');
                        console.log(`联动更新字段: ${updatedFieldsText}`);

                        // 显示更新成功提示
                        showUpdateNotification(memberName, '位置已更新');

                        // 显示所有变更
                        if (data.changes && data.changes.length > 0) {
                            console.log('详细变更:', data.changes);
                        }
                    }

                    updateStats();
                } else {
                    alert('更新失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新失败，请重试');
            });
        }

        function showUpdateNotification(memberName, message) {
            // 创建临时通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <strong>${memberName}</strong><br>
                <small>${message}</small>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // 小队折叠功能
        function toggleSquad(squadId) {
            const squadMembers = document.getElementById('squad-' + squadId);
            const icon = document.getElementById('icon-' + squadId);

            // 找到小队容器（可能是 .squad-section 或 .special-area）
            const squadSection = squadMembers.closest('.squad-section') || squadMembers.closest('.special-area');

            if (squadMembers.classList.contains('collapsed')) {
                squadMembers.classList.remove('collapsed');
                if (squadSection) {
                    squadSection.classList.remove('collapsed');
                }
                squadMembers.style.display = 'flex';
                if (icon) {
                    icon.textContent = '▼';
                }
            } else {
                squadMembers.classList.add('collapsed');
                if (squadSection) {
                    squadSection.classList.add('collapsed');
                }
                squadMembers.style.display = 'none';
                if (icon) {
                    icon.textContent = '▶';
                }
            }
        }


        
        function updateStats() {
            // 更新各小队人数
            updateSquadCounts();

            // 更新总览统计
            updateOverviewStats();
        }

        function updateOverviewStats() {
            const memberCards = document.querySelectorAll('.member-card');

            // 分别统计帮会成员和帮外人员
            let guildMembers = 0;
            let externalMembers = 0;

            memberCards.forEach(card => {
                const mainGroup = card.dataset.currentMainGroup;
                const subTeam = card.dataset.currentSubTeam;
                const squad = card.dataset.currentSquad;

                // 帮外人员不算在帮会成员中
                if (mainGroup === '其他团' && (subTeam === '帮外' || squad === '帮外')) {
                    externalMembers++;
                } else {
                    guildMembers++;
                }
            });

            // 统计各团人数（不包括帮外）
            const attackCount = document.querySelectorAll('[data-main-group="进攻团"] .member-card').length;
            const defenseCount = document.querySelectorAll('[data-main-group="防守团"] .member-card').length;

            // 其他团人数（不包括帮外）
            const otherCards = document.querySelectorAll('[data-main-group="其他团"] .member-card');
            let otherCount = 0;
            otherCards.forEach(card => {
                const subTeam = card.dataset.currentSubTeam;
                const squad = card.dataset.currentSquad;
                if (subTeam !== '帮外' && squad !== '帮外') {
                    otherCount++;
                }
            });

            // 更新显示
            const totalElement = document.getElementById('total-members');
            const attackElement = document.getElementById('attack-members');
            const defenseElement = document.getElementById('defense-members');
            const otherElement = document.getElementById('other-members');

            if (totalElement) totalElement.textContent = guildMembers;
            if (attackElement) attackElement.textContent = attackCount;
            if (defenseElement) defenseElement.textContent = defenseCount;
            if (otherElement) otherElement.textContent = otherCount;

            // 显示帮外人员数量
            const externalElement = document.getElementById('external-members');
            if (externalElement) {
                externalElement.textContent = externalMembers;
            }
        }

        function updateSquadCounts() {
            // 直接查找所有带有人数统计的元素
            const countElements = document.querySelectorAll('[id^="count-"]');

            countElements.forEach(countElement => {
                // 从count元素的ID获取对应的squad元素ID
                const countId = countElement.id;
                const squadId = countId.replace('count-', 'squad-');
                const squadElement = document.getElementById(squadId);

                if (squadElement) {
                    // 计算该小队的成员数量
                    const memberCards = squadElement.querySelectorAll('.member-card');
                    const memberCount = memberCards.length;
                    countElement.textContent = `现有${memberCount}人`;
                }
            });
        }

        function addNewTeam(teamType) {
            console.log('🏗️ 添加新团队:', teamType);

            if (teamType === '进攻团') {
                // 查找当前最大的团队编号
                const existingTeams = document.querySelectorAll('[data-main-group="进攻团"]');
                const teamNumbers = [];

                existingTeams.forEach(team => {
                    const subTeam = team.dataset.subTeam;
                    if (subTeam && subTeam.match(/^[一二三四五六七八九十]+团$/)) {
                        const numberMap = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10};
                        const number = numberMap[subTeam.charAt(0)];
                        if (number) teamNumbers.push(number);
                    }
                });

                const maxTeamNumber = Math.max(...teamNumbers, 0);
                const nextTeamNumber = maxTeamNumber + 1;

                if (nextTeamNumber > 10) {
                    alert('最多只能创建十团！');
                    return;
                }

                const numberToText = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '七', 8: '八', 9: '九', 10: '十'};
                const newTeamName = numberToText[nextTeamNumber] + '团';

                createNewTeamColumn('进攻团', newTeamName, 'attack');

            } else if (teamType === '防守团') {
                // 查找当前最大的防守团编号
                const existingDefenseTeams = document.querySelectorAll('[data-main-group="防守团"]');
                const defenseNumbers = [];

                existingDefenseTeams.forEach(team => {
                    const subTeam = team.dataset.subTeam;
                    if (subTeam === '防守团') {
                        defenseNumbers.push(1);
                    } else if (subTeam && subTeam.match(/^防守[一二三四五六七八九十]+团$/)) {
                        const numberMap = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10};
                        const numberChar = subTeam.charAt(2); // 防守X团中的X
                        const number = numberMap[numberChar];
                        if (number) defenseNumbers.push(number);
                    }
                });

                const maxDefenseNumber = Math.max(...defenseNumbers, 0);
                const nextDefenseNumber = maxDefenseNumber + 1;

                if (nextDefenseNumber > 10) {
                    alert('最多只能创建十个防守团！');
                    return;
                }

                const numberToText = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '七', 8: '八', 9: '九', 10: '十'};
                const newTeamName = nextDefenseNumber === 1 ? '防守团' : '防守' + numberToText[nextDefenseNumber] + '团';

                createNewTeamColumn('防守团', newTeamName, 'defense');

            } else {
                // 处理自定义主分组的子团队创建
                console.log('🏗️ 为自定义主分组创建子团队:', teamType);

                // 查找该主分组下现有的子团队
                const existingSubTeams = document.querySelectorAll(`[data-main-group="${teamType}"]`);
                const subTeamNumbers = [];

                existingSubTeams.forEach(team => {
                    const subTeam = team.dataset.subTeam;
                    // 包括主分组本身（主分组本身算作一团）
                    if (subTeam) {
                        if (subTeam === teamType) {
                            // 主分组本身算作一团
                            subTeamNumbers.push(1);
                        } else if (subTeam.match(/^[一二三四五六七八九十]+团$/)) {
                            // 匹配 "一团"、"二团" 等格式
                            const numberMap = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10};
                            const number = numberMap[subTeam.charAt(0)];
                            if (number) subTeamNumbers.push(number);
                        }
                    }
                });

                // 从现有最大编号+1开始
                const maxSubTeamNumber = Math.max(...subTeamNumbers, 0);
                const nextSubTeamNumber = maxSubTeamNumber + 1;

                if (nextSubTeamNumber > 10) {
                    alert('最多只能创建十个子团队！');
                    return;
                }

                const numberToText = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 6: '六', 7: '七', 8: '八', 9: '九', 10: '十'};
                const newSubTeamName = numberToText[nextSubTeamNumber] + '团';

                // 创建自定义主分组的子团队
                createCustomMainGroupSubTeam(teamType, newSubTeamName);
            }
        }

        function createCustomMainGroupSubTeam(mainGroup, subTeamName) {
            console.log('🏗️ 创建自定义主分组的子团队:', { mainGroup, subTeamName });

            // 获取主分组的颜色（从现有的主分组头部获取）
            const mainGroupHeader = document.querySelector(`[data-main-group="${mainGroup}"] .team-header`);
            let colorStyle = 'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;';
            if (mainGroupHeader) {
                colorStyle = mainGroupHeader.getAttribute('style');
            }

            // 创建团队列容器
            const teamColumn = document.createElement('div');
            teamColumn.className = 'team-column';

            // 创建团队头部
            const teamHeader = document.createElement('div');
            teamHeader.className = 'team-header custom-main-group';
            teamHeader.setAttribute('style', colorStyle);

            teamHeader.innerHTML = `
                <div class="team-title">
                    <span class="team-name-editable" ondblclick="editTeamName('${mainGroup}', '${subTeamName}', this)">${subTeamName}</span>
                </div>
                <div class="team-actions">
                    <button class="team-action-btn delete-team-btn" onclick="deleteTeam('${mainGroup}', '${subTeamName}')" title="删除团队">×</button>
                </div>
            `;
            teamColumn.appendChild(teamHeader);

            // 创建5个小队
            for (let i = 1; i <= 5; i++) {
                const squad = `${i}队`;

                // 创建小队区域
                const squadSection = document.createElement('div');
                squadSection.className = 'squad-section';
                squadSection.setAttribute('data-main-group', mainGroup);
                squadSection.setAttribute('data-sub-team', subTeamName);
                squadSection.setAttribute('data-squad', squad);

                // 创建小队标题
                const squadTitle = document.createElement('div');
                squadTitle.className = 'squad-title';
                squadTitle.onclick = () => toggleSquad(`${mainGroup}-${subTeamName}-${squad}`);
                squadTitle.innerHTML = `
                    ${squad} <span class="member-count" id="count-${mainGroup}-${subTeamName}-${squad}">现有0人</span>
                    <span class="collapse-icon" id="icon-${mainGroup}-${subTeamName}-${squad}">▼</span>
                `;

                // 创建小队成员容器
                const squadMembers = document.createElement('div');
                squadMembers.className = 'squad-members';
                squadMembers.id = `squad-${mainGroup}-${subTeamName}-${squad}`;
                squadMembers.setAttribute('data-main-group', mainGroup);
                squadMembers.setAttribute('data-sub-team', subTeamName);
                squadMembers.setAttribute('data-squad', squad);

                // 组装小队区域
                squadSection.appendChild(squadTitle);
                squadSection.appendChild(squadMembers);
                teamColumn.appendChild(squadSection);

                // 绑定拖拽事件
                bindDragEvents(squadSection);
            }

            // 将新团队列插入到该主分组的最后一个子团队之后
            const organizationBoard = document.querySelector('.organization-board');
            const mainGroupColumns = document.querySelectorAll(`[data-main-group="${mainGroup}"]`);

            if (mainGroupColumns.length > 0) {
                // 找到该主分组的最后一个列
                const lastMainGroupColumn = mainGroupColumns[mainGroupColumns.length - 1].closest('.team-column');
                organizationBoard.insertBefore(teamColumn, lastMainGroupColumn.nextElementSibling);
            } else {
                // 如果找不到主分组，插入到其他团之前
                const otherTeamColumn = document.querySelector('[data-main-group="其他团"]').closest('.team-column');
                organizationBoard.insertBefore(teamColumn, otherTeamColumn);
            }

            // 更新统计
            updateStats();

            // 保存到后端
            saveCustomTeamConfig(mainGroup, subTeamName);

            console.log('✅ 自定义主分组子团队创建完成:', subTeamName);
        }

        function createNewTeamColumn(mainGroup, subTeam, headerClass) {
            console.log('🏗️ 创建新团队列:', { mainGroup, subTeam, headerClass });

            // 创建团队列容器
            const teamColumn = document.createElement('div');
            teamColumn.className = 'team-column';

            // 创建团队头部
            const teamHeader = document.createElement('div');
            teamHeader.className = `team-header ${headerClass}`;
            const teamLabel = mainGroup === '进攻团' ? '(进攻)' : '(防守)';

            // 处理防守团的命名格式
            let displayName = subTeam;
            if (mainGroup === '防守团' && subTeam === '防守二团') {
                displayName = '二团';
            }

            teamHeader.innerHTML = `
                <div class="team-title">
                    <span class="team-name-editable" ondblclick="editTeamName('${mainGroup}', '${subTeam}', this)">${displayName}</span> ${teamLabel}
                </div>
                <div class="team-actions">
                    <button class="team-action-btn delete-team-btn" onclick="deleteTeam('${mainGroup}', '${subTeam}')" title="删除团队">×</button>
                </div>
            `;
            teamColumn.appendChild(teamHeader);

            // 创建5个小队
            for (let i = 1; i <= 5; i++) {
                const squad = `${i}队`;

                // 创建小队区域
                const squadSection = document.createElement('div');
                squadSection.className = 'squad-section';
                squadSection.setAttribute('data-main-group', mainGroup);
                squadSection.setAttribute('data-sub-team', subTeam);
                squadSection.setAttribute('data-squad', squad);

                // 创建小队标题
                const squadTitle = document.createElement('div');
                squadTitle.className = 'squad-title';
                squadTitle.onclick = () => toggleSquad(`${mainGroup}-${subTeam}-${squad}`);
                squadTitle.innerHTML = `
                    ${squad} <span class="member-count" id="count-${mainGroup}-${subTeam}-${squad}">现有0人</span>
                    <span class="collapse-icon" id="icon-${mainGroup}-${subTeam}-${squad}">▼</span>
                `;

                // 创建小队成员容器 - 关键：确保添加 data-* 属性！
                const squadMembers = document.createElement('div');
                squadMembers.className = 'squad-members';
                squadMembers.id = `squad-${mainGroup}-${subTeam}-${squad}`;
                squadMembers.setAttribute('data-main-group', mainGroup);
                squadMembers.setAttribute('data-sub-team', subTeam);
                squadMembers.setAttribute('data-squad', squad);

                // 组装小队区域
                squadSection.appendChild(squadTitle);
                squadSection.appendChild(squadMembers);
                teamColumn.appendChild(squadSection);

                // 绑定拖拽事件
                bindDragEvents(squadSection);
            }

            // 将新团队列插入到组织架构板中的正确位置
            const organizationBoard = document.querySelector('.organization-board');

            if (mainGroup === '进攻团') {
                // 进攻团插入到三团后面
                const threeTeamColumn = document.querySelector('[data-sub-team="三团"]').closest('.team-column');
                organizationBoard.insertBefore(teamColumn, threeTeamColumn.nextElementSibling);
            } else if (mainGroup === '防守团') {
                // 防守团插入到防守团后面
                const defenseTeamColumn = document.querySelector('[data-sub-team="防守团"]').closest('.team-column');
                organizationBoard.insertBefore(teamColumn, defenseTeamColumn.nextElementSibling);
            } else {
                // 其他情况插入到最后
                const lastColumn = organizationBoard.lastElementChild;
                organizationBoard.insertBefore(teamColumn, lastColumn);
            }

            // 更新统计
            updateStats();

            // 保存到后端
            saveTeamConfig(mainGroup, subTeam);

            console.log('✅ 新团队列创建完成:', subTeam);
        }

        function bindDragEvents(element) {
            // 绑定拖拽相关事件
            element.addEventListener('dragover', handleDragOver);
            element.addEventListener('dragenter', handleDragEnter);
            element.addEventListener('dragleave', handleDragLeave);
            element.addEventListener('drop', handleDrop);
        }

        function saveTeamConfig(teamType, subTeam) {
            console.log('💾 保存团队配置:', { teamType, subTeam });

            fetch('/save_team_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    'team_type': teamType,
                    'sub_team': subTeam
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ 团队配置保存成功:', data.message);
                    alert(`✅ ${subTeam}创建并保存成功！`);
                } else {
                    console.error('❌ 团队配置保存失败:', data.error);
                    alert(`❌ 保存失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('❌ 保存团队配置异常:', error);
                alert(`❌ 保存异常: ${error.message}`);
            });
        }

        function saveCustomTeamConfig(mainGroup, subTeam) {
            console.log('💾 保存自定义主分组团队配置:', { mainGroup, subTeam });

            fetch('/save_team_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    'main_group': mainGroup,
                    'sub_team': subTeam
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ 自定义团队配置保存成功:', data.message);
                    alert(`✅ ${mainGroup} - ${subTeam}创建并保存成功！`);
                } else {
                    console.error('❌ 自定义团队配置保存失败:', data.error);
                    alert(`❌ 保存失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('❌ 保存自定义团队配置异常:', error);
                alert(`❌ 保存异常: ${error.message}`);
            });
        }

        function loadTeamConfig() {
            console.log('📥 检查团队配置...');

            // 由于动态团队现在在服务器端渲染，这里只需要绑定事件
            const dynamicTeams = document.querySelectorAll('.team-column .squad-section');
            dynamicTeams.forEach(squadSection => {
                // 确保所有动态创建的团队都有拖拽事件
                bindDragEvents(squadSection);
            });

            console.log('✅ 动态团队事件绑定完成');
        }

        function deleteTeam(teamType, subTeam) {
            console.log('🗑️ 删除团队:', { teamType, subTeam });

            // 检查是否是默认团队
            const isDefaultTeam = (teamType === '进攻团' && ['一团', '二团', '三团'].includes(subTeam)) ||
                                 (teamType === '防守团' && subTeam === '防守团');

            // 检查团队中是否有成员
            const teamMembers = document.querySelectorAll(`[data-current-main-group="${teamType}"][data-current-sub-team="${subTeam}"]`);

            let confirmMessage;
            if (teamMembers.length > 0) {
                const memberNames = Array.from(teamMembers).map(card => card.dataset.memberName).join(', ');
                confirmMessage = `团队 ${subTeam} 中还有 ${teamMembers.length} 名成员：${memberNames}\n\n删除团队将把这些成员移动到替补区域。\n\n确定要删除吗？`;
            } else {
                confirmMessage = `确定要删除 ${subTeam} 吗？`;
            }

            if (isDefaultTeam) {
                confirmMessage += `\n\n⚠️ 注意：${subTeam} 是默认团队，删除后将无法通过"+"按钮恢复，需要手动重新创建！`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            // 将成员移动到替补区域
            if (teamMembers.length > 0) {
                const substituteArea = document.getElementById('squad-其他团-替补-替补');
                teamMembers.forEach(memberCard => {
                    substituteArea.appendChild(memberCard);

                    // 更新成员数据
                    memberCard.dataset.currentMainGroup = '其他团';
                    memberCard.dataset.currentSubTeam = '替补';
                    memberCard.dataset.currentSquad = '替补';

                    // 保存到服务器
                    updateMemberPosition(memberCard.dataset.memberName, '其他团', '替补', '替补');
                });
            }

            if (isDefaultTeam) {
                // 默认团队：直接从DOM删除，不需要删除配置文件
                const teamColumn = document.querySelector(`[data-sub-team="${subTeam}"]`).closest('.team-column');
                if (teamColumn) {
                    teamColumn.remove();
                }

                // 更新统计
                updateStats();

                alert(`✅ ${subTeam}删除成功！\n\n💡 提示：如需恢复，请手动重新创建团队。`);
            } else {
                // 动态团队：删除配置文件
                deleteTeamConfig(teamType, subTeam);
            }
        }

        function deleteTeamConfig(teamType, subTeam) {
            console.log('🗑️ 删除团队配置:', { teamType, subTeam });

            fetch('/delete_team_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    'team_type': teamType,
                    'sub_team': subTeam
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ 团队配置删除成功:', data.message);

                    // 从DOM中移除团队列
                    const teamColumn = document.querySelector(`[data-sub-team="${subTeam}"]`).closest('.team-column');
                    if (teamColumn) {
                        teamColumn.remove();
                    }

                    // 更新统计
                    updateStats();

                    alert(`✅ ${subTeam}删除成功！`);
                } else {
                    console.error('❌ 团队配置删除失败:', data.error);
                    alert(`❌ 删除失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('❌ 删除团队配置异常:', error);
                alert(`❌ 删除异常: ${error.message}`);
            });
        }

        // 成员编辑相关函数
        function showMemberEditMenu(memberName, event) {
            event.preventDefault();
            event.stopPropagation();

            // 获取成员卡片信息
            const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
            if (!memberCard) {
                alert('找不到成员信息！');
                return;
            }

            // 直接调用职责+技能编辑功能
            editMemberPositionAndSkills(memberName, memberCard.dataset.position, memberCard.dataset.skills);
        }

        function showFullMemberEdit(memberName) {
            // 关闭选项菜单
            const menu = document.getElementById('memberOptionsMenu');
            if (menu) menu.remove();

            console.log('🔧 显示完整成员编辑菜单:', memberName);
            currentEditingMemberName = memberName;

            // 获取成员卡片信息
            const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
            if (!memberCard) {
                alert('找不到成员信息！');
                return;
            }

            const profession = memberCard.dataset.profession;
            const position = memberCard.dataset.position;

            // 填充表单
            document.getElementById('editMemberName').value = memberName;
            document.getElementById('editMemberProfession').value = profession;
            document.getElementById('editMemberPosition').value = position;

            // 显示模态框
            const modal = document.getElementById('memberEditMenu');
            if (modal) {
                modal.style.display = 'block';
            }
        }

        function closeMemberEditMenu() {
            const modal = document.getElementById('memberEditMenu');
            if (modal) {
                modal.style.display = 'none';
            }
            currentEditingMemberName = null;
        }

        function saveMemberEdit() {
            if (!currentEditingMemberName) {
                alert('系统错误：未找到编辑的成员！');
                return;
            }

            const newName = document.getElementById('editMemberName').value.trim();
            const newProfession = document.getElementById('editMemberProfession').value;
            const newPosition = document.getElementById('editMemberPosition').value;

            if (!newName) {
                alert('请输入成员姓名！');
                return;
            }

            // 检查新姓名是否与其他成员重复
            if (newName !== currentEditingMemberName) {
                const existingMember = document.querySelector(`[data-member-name="${newName}"]`);
                if (existingMember) {
                    alert('该成员名已存在！');
                    return;
                }
            }

            console.log('💾 保存成员修改:', {
                oldName: currentEditingMemberName,
                newName: newName,
                profession: newProfession,
                position: newPosition
            });

            // 发送到后端
            fetch('/update_member_info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    old_name: currentEditingMemberName,
                    new_name: newName,
                    profession: newProfession,
                    position: newPosition
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新成员卡片
                    const memberCard = document.querySelector(`[data-member-name="${currentEditingMemberName}"]`);
                    if (memberCard) {
                        memberCard.dataset.memberName = newName;
                        memberCard.dataset.profession = newProfession;
                        memberCard.dataset.position = newPosition;

                        // 更新显示内容
                        const nameText = memberCard.childNodes[0];
                        if (nameText && nameText.nodeType === Node.TEXT_NODE) {
                            nameText.textContent = newName;
                        }

                        const positionBadge = memberCard.querySelector('.position-badge');
                        if (positionBadge) {
                            positionBadge.textContent = newPosition;
                        }

                        // 更新按钮onclick
                        const editBtn = memberCard.querySelector('.edit-btn');
                        if (editBtn) {
                            editBtn.setAttribute('onclick', `showMemberEditMenu('${newName}', event)`);
                        }
                    }

                    closeMemberEditMenu();
                    alert('成员信息修改成功！');
                } else {
                    alert('修改失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('修改失败:', error);
                alert('修改失败：' + error.message);
            });
        }

        function deleteMember() {
            if (!currentEditingMemberName) {
                alert('系统错误：未找到要删除的成员！');
                return;
            }

            if (!confirm(`确定要删除成员 "${currentEditingMemberName}" 吗？此操作不可撤销！`)) {
                return;
            }

            console.log('🗑️ 删除成员:', currentEditingMemberName);

            // 发送到后端
            fetch('/delete_member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    member_name: currentEditingMemberName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 移除成员卡片
                    const memberCard = document.querySelector(`[data-member-name="${currentEditingMemberName}"]`);
                    if (memberCard) {
                        memberCard.remove();
                    }

                    // 更新统计
                    updateStats();

                    closeMemberEditMenu();
                    alert('成员删除成功！');
                } else {
                    alert('删除失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                alert('删除失败：' + error.message);
            });
        }

        function deleteMemberDirect(memberName) {
            console.log('🗑️ 直接删除成员:', memberName);

            // 发送到后端
            fetch('/delete_member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    member_name: memberName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 移除成员卡片
                    const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
                    if (memberCard) {
                        memberCard.remove();
                    }

                    // 更新统计
                    updateStats();

                    alert('成员删除成功！');
                } else {
                    alert('删除失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                alert('删除失败：' + error.message);
            });
        }

        // 快速新建成员相关函数
        function showQuickCreateModal() {
            console.log('🚀 显示快速新建模态框');
            const modal = document.getElementById('quickCreateModal');
            if (modal) {
                modal.style.display = 'block';

                // 清空表单
                document.getElementById('quickMemberName').value = '';
                document.getElementById('quickMemberProfession').value = '';
                document.getElementById('quickMemberPosition').value = '拆塔';

                // 聚焦到姓名输入框
                setTimeout(() => {
                    document.getElementById('quickMemberName').focus();
                }, 100);
            }
        }

        function closeQuickCreateModal() {
            const modal = document.getElementById('quickCreateModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        function quickCreateMember() {
            console.log('🚀 快速创建成员');

            const name = document.getElementById('quickMemberName').value.trim();
            const profession = document.getElementById('quickMemberProfession').value;
            const position = document.getElementById('quickMemberPosition').value;

            console.log('📊 快速创建数据:', { name, profession, position });

            if (!name) {
                alert('请输入成员姓名！');
                return;
            }

            if (!profession) {
                alert('请选择职业！');
                return;
            }

            // 检查成员名是否已存在
            const existingMember = document.querySelector(`[data-member-name="${name}"]`);
            if (existingMember) {
                alert('该成员名已存在！');
                return;
            }

            const memberData = {
                name: name,
                profession: profession,
                position: position
            };

            console.log('🚀 调用createNewMember:', memberData);

            // 关闭模态框
            closeQuickCreateModal();

            // 创建成员
            createNewMember(memberData);
        }

        // 直接测试创建成员（不依赖表单）
        function directTestCreate() {
            console.log('🧪 直接测试创建成员功能');

            // 使用固定的测试数据
            const testData = {
                name: '测试新建' + Date.now(), // 使用时间戳确保唯一性
                profession: '素问',
                position: '治疗'
            };

            console.log('📊 测试数据:', testData);
            console.log('🚀 直接调用createNewMember:', testData);

            // 直接调用创建函数
            createNewMember(testData);
        }

        // 测试创建成员函数
        function testCreateMember() {
            console.log('🧪 测试创建成员功能');

            const nameInput = document.getElementById('newMemberName');
            const professionInput = document.getElementById('newMemberProfession');
            const positionInput = document.getElementById('newMemberPosition');

            const name = nameInput ? nameInput.value.trim() : '';
            const profession = professionInput ? professionInput.value : '';
            const position = positionInput ? positionInput.value : '拆塔';

            console.log('📊 当前表单数据:', { name, profession, position });

            if (!name) {
                alert('请输入成员姓名！');
                return;
            }

            if (!profession) {
                alert('请选择职业！');
                return;
            }

            // 检查成员名是否已存在
            const existingMember = document.querySelector(`[data-member-name="${name}"]`);
            if (existingMember) {
                alert('该成员名已存在！');
                return;
            }

            const memberData = {
                name: name,
                profession: profession,
                position: position
            };

            console.log('🚀 直接调用createNewMember:', memberData);
            createNewMember(memberData);
        }

        // 调试函数
        function debugNewMember() {
            console.log('🔧 开始调试新建成员功能');

            // 检查按钮
            const btn = document.getElementById('newMemberBtn');
            console.log('🔘 新建成员按钮:', btn);

            // 检查模态框
            const modal = document.getElementById('newMemberModal');
            console.log('📋 新建成员模态框:', modal);

            // 检查表单
            const form = document.getElementById('newMemberForm');
            console.log('📝 新建成员表单:', form);

            // 检查输入框
            const nameInput = document.getElementById('newMemberName');
            const professionInput = document.getElementById('newMemberProfession');
            const positionInput = document.getElementById('newMemberPosition');
            console.log('📊 输入框:', { nameInput, professionInput, positionInput });

            // 尝试显示模态框
            if (modal) {
                modal.style.display = 'block';
                console.log('✅ 强制显示模态框');
            }
        }

        // 新建成员相关函数
        function showNewMemberModal() {
            console.log('🔍 尝试显示新建成员模态框');

            try {
                const newMemberModal = document.getElementById('newMemberModal');
                console.log('📋 找到模态框元素:', newMemberModal);
                console.log('📋 模态框当前display:', newMemberModal ? newMemberModal.style.display : 'null');

                if (newMemberModal) {
                    // 强制设置样式
                    newMemberModal.style.display = 'block';
                    newMemberModal.style.visibility = 'visible';
                    newMemberModal.style.opacity = '1';
                    console.log('✅ 模态框已显示');
                    console.log('📋 模态框设置后display:', newMemberModal.style.display);
                } else {
                    console.error('❌ 未找到新建成员模态框');
                    alert('错误：未找到新建成员模态框');
                    return;
                }

                // 清空表单
                const newMemberForm = document.getElementById('newMemberForm');
                if (newMemberForm) {
                    newMemberForm.reset();
                    console.log('📝 表单已重置');
                } else {
                    console.error('❌ 未找到新建成员表单');
                }

                // 设置默认职责
                const newMemberPosition = document.getElementById('newMemberPosition');
                if (newMemberPosition) {
                    newMemberPosition.value = '拆塔';
                    console.log('🎯 默认职责已设置为拆塔');
                } else {
                    console.error('❌ 未找到职责选择框');
                }

                // 聚焦到姓名输入框
                const nameInput = document.getElementById('newMemberName');
                if (nameInput) {
                    setTimeout(() => nameInput.focus(), 100);
                }

            } catch (error) {
                console.error('❌ showNewMemberModal出现异常:', error);
                alert('显示模态框时出现错误: ' + error.message);
            }
        }

        function closeNewMemberModal() {
            const newMemberModal = document.getElementById('newMemberModal');
            if (newMemberModal) {
                newMemberModal.style.display = 'none';
            }
        }

        function createNewMember(memberData) {
            console.log('创建新成员:', memberData);

            fetch('/add_member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(memberData)
            })
            .then(response => {
                console.log('收到响应，状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('解析JSON成功，数据:', data);
                if (data && data.success) {
                    console.log('🎉 成员创建成功！');

                    // 创建新的成员卡片
                    const newMemberCard = createMemberCard(memberData);

                    // 添加到替补区域
                    const substituteArea = document.getElementById('squad-其他团-替补-替补');
                    console.log('🎯 查找替补区域:', substituteArea);

                    if (substituteArea) {
                        substituteArea.appendChild(newMemberCard);
                        console.log('✅ 成员卡片已添加到替补区域');
                    } else {
                        console.error('❌ 未找到替补区域，尝试其他位置');
                        // 尝试找到任何一个小队区域作为备选
                        const anySquadArea = document.querySelector('[id^="squad-"]');
                        if (anySquadArea) {
                            anySquadArea.appendChild(newMemberCard);
                            console.log('✅ 成员卡片已添加到备选区域:', anySquadArea.id);
                        } else {
                            console.error('❌ 找不到任何小队区域');
                        }
                    }

                    // 更新人数统计
                    updateStats();

                    // 关闭模态框
                    closeNewMemberModal();

                    // 显示成功提示
                    alert(`成员 ${memberData.name} 创建成功！`);
                } else {
                    console.log('❌ 服务器返回失败，数据:', data);
                    alert('创建失败：' + (data ? data.error : '未知错误'));
                }
            })
            .catch(error => {
                console.error('❌ 请求出现异常:', error);
                alert('请求异常：' + error.message);
            });
        }

        function createMemberCard(memberData) {
            console.log('🎨 创建成员卡片:', memberData);
            const card = document.createElement('div');
            card.className = 'member-card';
            card.draggable = true;

            // 设置数据属性 - 新建成员默认归属替补
            card.dataset.memberName = memberData.name;
            card.dataset.profession = memberData.profession;
            card.dataset.position = memberData.position;
            card.dataset.currentMainGroup = '其他团';
            card.dataset.currentSubTeam = '替补';
            card.dataset.currentSquad = '替补';

            // 设置HTML内容
            card.innerHTML = `
                ${memberData.name}
                <button class="edit-btn" onclick="showMemberEditMenu('${memberData.name}', event)" title="编辑成员" style="
                    background: none;
                    border: none;
                    font-size: 14px;
                    cursor: pointer;
                    padding: 2px 4px;
                    border-radius: 3px;
                    color: #666;
                    transition: all 0.2s ease;
                " onmouseover="this.style.background='#f0f0f0'; this.style.color='#333';" onmouseout="this.style.background='none'; this.style.color='#666';">
                    <i class="fas fa-cog"></i>
                </button>
                <span class="position-badge">${memberData.position}</span>
            `;

            // 添加事件监听器
            card.addEventListener('dragstart', handleDragStart);
            card.addEventListener('dragend', handleDragEnd);
            card.addEventListener('dblclick', handleDoubleClick);

            console.log('✅ 成员卡片创建完成:', card);
            return card;
        }

        // 完整成员编辑函数
        function editMemberPositionAndSkills(memberName, currentPosition, currentSkills) {
            console.log('编辑成员信息:', memberName, currentPosition, currentSkills);

            currentEditingMember = memberName;

            // 解析技能数据
            const skillsArray = currentSkills ? currentSkills.split(',').filter(s => s.trim()) : [];
            currentEditingMemberSkills = [...skillsArray];
            currentEditingMemberForSkills = memberName;

            // 获取成员完整信息
            const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
            if (!memberCard) {
                alert('找不到成员信息！');
                return;
            }

            // 加载技能预设（如果还没加载）
            if (skillPresets.length === 0) {
                loadSkillPresets().then(() => {
                    showCompleteEditModal(memberName, memberCard);
                });
            } else {
                showCompleteEditModal(memberName, memberCard);
            }
        }

        // 职责编辑相关函数（保留原有功能）
        function editMemberPosition(memberName, currentPosition) {
            console.log('编辑成员职责:', memberName, currentPosition);

            currentEditingMember = memberName;

            // 安全检查DOM元素
            const editingMemberNameElement = document.getElementById('editingMemberName');
            if (editingMemberNameElement) {
                editingMemberNameElement.textContent = memberName;
            }

            // 清除之前的选择
            document.querySelectorAll('.position-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 高亮当前职责
            const currentOption = document.querySelector(`[data-position="${currentPosition}"]`);
            if (currentOption) {
                currentOption.classList.add('selected');
            }

            // 显示模态框
            const positionModal = document.getElementById('positionModal');
            if (positionModal) {
                positionModal.style.display = 'block';
            } else {
                console.error('找不到职责编辑模态框');
            }
        }

        function closePositionModal() {
            const positionModal = document.getElementById('positionModal');
            if (positionModal) {
                positionModal.style.display = 'none';
            }
            currentEditingMember = null;
        }

        function switchToFullEdit() {
            if (!currentEditingMember) {
                alert('系统错误：未找到编辑的成员！');
                return;
            }

            const memberName = currentEditingMember;

            // 关闭职责编辑模态框
            closePositionModal();

            // 打开完整编辑模态框
            showFullMemberEdit(memberName);
        }

        function selectPosition(newPosition) {
            console.log('selectPosition 被调用:', newPosition);
            console.log('currentEditingMember:', currentEditingMember);

            if (!currentEditingMember) {
                alert('系统错误：未找到编辑的成员！');
                return;
            }

            // 保存成员名称，避免在关闭模态框时被清空
            const memberName = currentEditingMember;

            // 更新成员卡片
            const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
            if (memberCard) {
                memberCard.dataset.position = newPosition;
                const positionBadge = memberCard.querySelector('.position-badge');
                if (positionBadge) {
                    positionBadge.textContent = newPosition;
                }
            }

            // 关闭模态框
            closePositionModal();

            // 发送到后端更新
            console.log('准备发送请求...');
            console.log('发送数据:', {
                'member_name': memberName,
                'position': newPosition
            });
            console.log('请求URL:', '/update_member_position_only');

            fetch('/update_member_position_only', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    'member_name': memberName,
                    'position': newPosition
                })
            })
            .then(response => {
                console.log('✅ 收到响应，状态:', response.status);
                console.log('✅ 响应OK:', response.ok);
                return response.json();
            })
            .then(data => {
                console.log('✅ 解析JSON成功，数据:', data);
                if (data && data.success) {
                    console.log('🎉 更新成功！');
                    // 简单的成功提示 - 不需要弹窗
                    console.log(`✅ ${memberName} 的职责已更新为：${newPosition}`);

                    // 可选：添加简单的视觉反馈
                    const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
                    if (memberCard) {
                        // 短暂的成功动画
                        memberCard.style.transform = 'scale(1.05)';
                        memberCard.style.transition = 'transform 0.2s ease';
                        setTimeout(() => {
                            memberCard.style.transform = 'scale(1)';
                        }, 200);
                    }
                } else {
                    console.log('❌ 服务器返回失败，数据:', data);
                    alert('更新失败：' + (data ? data.error : '未知错误'));
                }
            })
            .catch(error => {
                console.error('❌ 请求出现异常:', error);
                console.error('❌ 错误类型:', error.name);
                console.error('❌ 错误消息:', error.message);
                console.error('❌ 错误堆栈:', error.stack);
                alert('请求异常：' + error.message);
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化模态框事件
            const positionModal = document.getElementById('positionModal');
            if (positionModal) {
                positionModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closePositionModal();
                    }
                });
            }

            // 初始化新建成员模态框事件
            const newMemberModal = document.getElementById('newMemberModal');
            if (newMemberModal) {
                newMemberModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeNewMemberModal();
                    }
                });
            }

            // 初始化快速新建模态框事件
            const quickCreateModal = document.getElementById('quickCreateModal');
            if (quickCreateModal) {
                quickCreateModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeQuickCreateModal();
                    }
                });
            }

            // 初始化成员编辑模态框事件
            const memberEditMenu = document.getElementById('memberEditMenu');
            if (memberEditMenu) {
                memberEditMenu.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeMemberEditMenu();
                    }
                });
            }

            // 初始化新建成员表单提交事件
            const newMemberForm = document.getElementById('newMemberForm');
            console.log('🔍 查找新建成员表单:', newMemberForm);

            if (newMemberForm) {
                console.log('✅ 找到新建成员表单，添加提交事件监听器');

                // 移除可能存在的旧事件监听器
                newMemberForm.onsubmit = null;

                // 添加新的事件监听器
                newMemberForm.addEventListener('submit', function(e) {
                    console.log('📝 表单提交事件触发');
                    e.preventDefault();
                    e.stopPropagation();

                    const nameInput = document.getElementById('newMemberName');
                    const professionInput = document.getElementById('newMemberProfession');
                    const positionInput = document.getElementById('newMemberPosition');

                    const name = nameInput ? nameInput.value.trim() : '';
                    const profession = professionInput ? professionInput.value : '';
                    const position = positionInput ? positionInput.value : '拆塔';

                    console.log('📊 表单数据:', { name, profession, position });

                    if (!name) {
                        alert('请输入成员姓名！');
                        return;
                    }

                    if (!profession) {
                        alert('请选择职业！');
                        return;
                    }

                    // 检查成员名是否已存在
                    const existingMember = document.querySelector(`[data-member-name="${name}"]`);
                    if (existingMember) {
                        alert('该成员名已存在！');
                        return;
                    }

                    const memberData = {
                        name: name,
                        profession: profession,
                        position: position
                    };

                    console.log('🚀 准备创建新成员:', memberData);
                    createNewMember(memberData);
                });

                // 同时为提交按钮添加点击事件作为备选
                const submitBtn = document.getElementById('createMemberBtn');
                if (submitBtn) {
                    console.log('✅ 找到提交按钮，添加点击事件监听器');
                    submitBtn.addEventListener('click', function(e) {
                        console.log('🖱️ 提交按钮被点击');
                        // 触发表单提交
                        if (newMemberForm) {
                            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            newMemberForm.dispatchEvent(submitEvent);
                        }
                    });
                }
            } else {
                console.error('❌ 未找到新建成员表单');
            }

            // 延迟初始化，确保DOM完全加载
            setTimeout(() => {
                // 加载团队配置（恢复动态创建的团队）
                loadTeamConfig();

                // 初始化统计
                updateStats();

                // 调试新建成员功能
                console.log('🔧 页面加载完成，开始调试新建成员功能');
                const newMemberBtn = document.getElementById('newMemberBtn');
                const newMemberModal = document.getElementById('newMemberModal');
                const newMemberForm = document.getElementById('newMemberForm');

                console.log('🔘 新建成员按钮:', newMemberBtn);
                console.log('📋 新建成员模态框:', newMemberModal);
                console.log('📝 新建成员表单:', newMemberForm);
                console.log('🔧 showNewMemberModal函数:', typeof showNewMemberModal);

                // 测试按钮点击
                if (newMemberBtn) {
                    console.log('✅ 找到新建成员按钮，添加点击事件监听器');
                    newMemberBtn.addEventListener('click', function(e) {
                        console.log('🖱️ 新建成员按钮被点击了！');
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('🔄 手动调用showNewMemberModal');
                        showNewMemberModal();
                    });

                    // 同时测试onclick属性
                    console.log('🔍 按钮onclick属性:', newMemberBtn.onclick);
                    console.log('🔍 按钮getAttribute onclick:', newMemberBtn.getAttribute('onclick'));
                } else {
                    console.error('❌ 未找到新建成员按钮');
                }



                console.log('初始化完成');
            }, 100);

            const memberCards = document.querySelectorAll('.member-card');
            memberCards.forEach(card => {
                // 绑定双击事件（确保所有卡片都有）
                card.addEventListener('dblclick', handleDoubleClick);

                const mainGroup = card.dataset.currentMainGroup;
                const subTeam = card.dataset.currentSubTeam;
                const squad = card.dataset.currentSquad;

                // 构建目标ID
                let squadId;
                if (mainGroup === '其他团' && (subTeam === '请假' || squad === '请假')) {
                    squadId = 'squad-其他团-请假-请假';
                } else if (mainGroup === '其他团' && (subTeam === '替补' || squad === '替补')) {
                    squadId = 'squad-其他团-替补-替补';
                } else if (mainGroup === '其他团' && (subTeam === '帮外' || squad === '帮外')) {
                    squadId = 'squad-其他团-帮外-帮外';
                } else {
                    squadId = `squad-${mainGroup.replace(/\s/g, '_')}-${subTeam.replace(/\s/g, '_')}-${squad.replace(/\s/g, '_')}`;
                }

                const targetSquad = document.getElementById(squadId);
                if (targetSquad) {
                    targetSquad.appendChild(card);
                } else {
                    // 如果找不到目标位置，放回成员池
                    const memberPool = document.getElementById('member-pool-container');
                    if (memberPool) {
                        memberPool.appendChild(card);
                    }
                }
            });
        });

        // 双击编辑团队名称
        function editTeamName(mainGroup, subTeam, element) {
            console.log('✏️ 编辑团队名称:', { mainGroup, subTeam });

            const currentName = element.textContent.trim();
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentName;
            input.className = 'team-name-input';
            input.style.cssText = `
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #007bff;
                border-radius: 4px;
                padding: 2px 6px;
                font-size: 14px;
                font-weight: bold;
                width: 80px;
                text-align: center;
            `;

            // 替换文本为输入框
            element.style.display = 'none';
            element.parentNode.insertBefore(input, element);
            input.focus();
            input.select();

            // 保存函数
            function saveTeamName() {
                const newName = input.value.trim();
                if (!newName) {
                    alert('团队名称不能为空！');
                    input.focus();
                    return;
                }

                if (newName === currentName) {
                    // 没有变化，直接恢复
                    element.style.display = 'inline';
                    input.remove();
                    return;
                }

                // 发送更新请求
                fetch('/update_team_name', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        main_group: mainGroup,
                        sub_team: subTeam,
                        display_name: newName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        element.textContent = newName;
                        element.style.display = 'inline';
                        input.remove();

                        // 刷新页面以更新所有相关显示
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);

                        showNotification('✅ ' + data.message, 'success');
                    } else {
                        alert('❌ 更新失败：' + (data.error || '未知错误'));
                        input.focus();
                    }
                })
                .catch(error => {
                    console.error('更新团队名称失败:', error);
                    alert('❌ 更新失败：网络错误');
                    input.focus();
                });
            }

            // 取消函数
            function cancelEdit() {
                element.style.display = 'inline';
                input.remove();
            }

            // 绑定事件
            input.addEventListener('blur', saveTeamName);
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTeamName();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });
        }



        // 新建主分组相关函数
        function showCreateMainGroupDialog() {
            document.getElementById('createMainGroupModal').style.display = 'flex';
            document.getElementById('mainGroupName').focus();
        }

        function closeCreateMainGroupDialog() {
            document.getElementById('createMainGroupModal').style.display = 'none';
            // 清空表单
            document.getElementById('mainGroupName').value = '';
            document.getElementById('mainGroupType').value = 'attack';
            document.getElementById('mainGroupDescription').value = '';
        }

        function createMainGroup() {
            const name = document.getElementById('mainGroupName').value.trim();
            const type = document.getElementById('mainGroupType').value;
            const description = document.getElementById('mainGroupDescription').value.trim();

            if (!name) {
                alert('请输入主分组名称！');
                return;
            }

            // 发送创建请求到后端
            fetch('/create_main_group', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    type: type,
                    description: description
                })
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('服务器响应:', data);

                if (data.success) {
                    // 关闭对话框
                    closeCreateMainGroupDialog();

                    // 显示成功提示
                    showNotification(`✅ ${data.message}`, 'success');

                    // 延迟刷新页面，让用户看到成功提示
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    console.error('服务器返回错误:', data.error);
                    alert('❌ 创建失败：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('创建主分组失败:', error);
                alert('❌ 创建失败：' + error.message + '\n\n请检查：\n1. 网络连接是否正常\n2. 服务器是否运行\n3. 是否有管理员权限');
            });
        }

        // 定义丰富的颜色系统 - 移到全局作用域
        window.mainGroupColors = [
            { name: 'purple', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', text: 'white' },
            { name: 'blue', gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', text: 'white' },
            { name: 'green', gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', text: 'white' },
            { name: 'orange', gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', text: 'white' },
            { name: 'pink', gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', text: '#333' },
            { name: 'teal', gradient: 'linear-gradient(135deg, #30cfd0 0%, #91a7ff 100%)', text: 'white' },
            { name: 'indigo', gradient: 'linear-gradient(135deg, #a8c0ff 0%, #3f2b96 100%)', text: 'white' },
            { name: 'cyan', gradient: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)', text: 'white' },
            { name: 'emerald', gradient: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)', text: 'white' },
            { name: 'amber', gradient: 'linear-gradient(135deg, #fdcb6e 0%, #e17055 100%)', text: 'white' }
        ];

        window.colorIndex = 0; // 全局颜色索引

        function getNextColor() {
            const color = window.mainGroupColors[window.colorIndex % window.mainGroupColors.length];
            window.colorIndex++;
            return color;
        }

        function createMainGroupColumn(name, type, description) {
            const organizationBoard = document.querySelector('.organization-board');

            // 创建主分组容器
            const mainGroupContainer = document.createElement('div');
            mainGroupContainer.className = 'team-column';
            mainGroupContainer.style.marginRight = '20px';

            // 获取下一个颜色
            const colorScheme = getNextColor();

            // 创建完整的主分组HTML，包含5个小队，格式与进攻团/防守团一致
            let squadSectionsHTML = '';
            for (let i = 1; i <= 5; i++) {
                squadSectionsHTML += `
                    <div class="squad-section" data-main-group="${name}" data-sub-team="${name}" data-squad="${i}队">
                        <div class="squad-title" onclick="toggleSquad('${name}-${name}-${i}队')">
                            ${i}队 <span class="member-count" id="count-${name}-${name}-${i}队">现有0人</span>
                            <span class="collapse-icon" id="icon-${name}-${name}-${i}队">▼</span>
                        </div>
                        <div class="squad-members" id="squad-${name}-${name}-${i}队"
                             data-main-group="${name}"
                             data-sub-team="${name}"
                             data-squad="${i}队">
                        </div>
                    </div>
                `;
            }

            mainGroupContainer.innerHTML = `
                <div class="team-header custom-main-group" style="background: ${colorScheme.gradient}; color: ${colorScheme.text};">
                    <div class="team-title">
                        <span class="team-name-editable" ondblclick="editTeamName('${name}', '${name}', this)">${name}</span>
                    </div>
                    <div class="team-actions">
                        <button class="team-action-btn add-team-btn" onclick="addNewTeam('${name}')" title="添加子团队">+</button>
                        <button class="team-action-btn delete-team-btn" onclick="deleteMainGroup('${name}')" title="删除主分组">×</button>
                    </div>
                </div>
                ${squadSectionsHTML}
            `;

            // 添加到页面末尾（其他团之前）
            const otherTeamColumn = document.querySelector('[data-main-group="其他团"]').closest('.team-column');
            organizationBoard.insertBefore(mainGroupContainer, otherTeamColumn);

            // 为新创建的所有小队容器添加拖拽事件
            const newSquadSections = mainGroupContainer.querySelectorAll('.squad-section');
            newSquadSections.forEach(section => {
                section.addEventListener('dragover', handleDragOver);
                section.addEventListener('drop', handleDrop);
                section.addEventListener('dragenter', handleDragEnter);
                section.addEventListener('dragleave', handleDragLeave);
            });

            console.log(`✅ 主分组 "${name}" 创建成功，包含5个小队`);
        }

        function deleteTeam(mainGroup, subTeam) {
            if (confirm(`确定要删除团队 "${subTeam}" 吗？\n\n注意：删除后该团队下的所有成员将移动到替补区域。`)) {
                console.log(`🗑️ 开始删除团队: ${mainGroup}-${subTeam}`);

                // 发送删除请求到后端
                fetch('/delete_team_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        main_group: mainGroup,
                        sub_team: subTeam
                    })
                })
                .then(response => {
                    console.log('删除响应状态:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('删除服务器响应:', data);

                    if (data.success) {
                        // 删除成功，刷新页面显示最新状态
                        showNotification(`✅ ${data.message}`, 'success');

                        // 延迟刷新页面，让用户看到成功提示
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        console.error('服务器返回错误:', data.error);
                        alert('❌ 删除失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('删除团队失败:', error);
                    alert('❌ 删除失败：' + error.message + '\n\n请检查：\n1. 网络连接是否正常\n2. 服务器是否运行\n3. 是否有管理员权限');
                });
            }
        }

        function deleteMainGroup(mainGroupName) {
            if (confirm(`确定要删除主分组 "${mainGroupName}" 吗？\n\n注意：删除后该分组下的所有成员将移动到替补区域。`)) {
                console.log(`🗑️ 开始删除主分组: ${mainGroupName}`);

                // 发送删除请求到后端
                fetch('/delete_main_group', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: mainGroupName
                    })
                })
                .then(response => {
                    console.log('删除响应状态:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('删除服务器响应:', data);

                    if (data.success) {
                        // 删除成功，刷新页面显示最新状态
                        showNotification(`✅ ${data.message}`, 'success');

                        // 延迟刷新页面，让用户看到成功提示
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        console.error('服务器返回错误:', data.error);
                        alert('❌ 删除失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('删除主分组失败:', error);
                    alert('❌ 删除失败：' + error.message + '\n\n请检查：\n1. 网络连接是否正常\n2. 服务器是否运行\n3. 是否有管理员权限');
                });
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');

            let backgroundColor;
            switch(type) {
                case 'success':
                    backgroundColor = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                    break;
                case 'error':
                    backgroundColor = 'linear-gradient(135deg, #dc3545 0%, #fd7e14 100%)';
                    break;
                case 'info':
                default:
                    backgroundColor = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    break;
            }

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${backgroundColor};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
</script>

<!-- 新建主分组对话框 -->
<div id="createMainGroupModal" class="position-modal" style="display: none;">
    <div class="position-modal-content" style="max-width: 400px;">
        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">
            <i class="fas fa-plus-circle"></i> 新建主分组
        </h3>

        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">主分组名称 *</label>
            <input type="text" id="mainGroupName" placeholder="例如：机动团、特战团、后勤团等"
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">分组类型</label>
            <select id="mainGroupType" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px;">
                <option value="attack">进攻类型</option>
                <option value="defense">防守类型</option>
                <option value="special">特殊类型</option>
            </select>
        </div>

        <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">说明</label>
            <textarea id="mainGroupDescription" placeholder="可选：简单描述这个主分组的用途"
                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; height: 60px; resize: vertical;"></textarea>
        </div>

        <div style="text-align: center;">
            <button type="button" onclick="closeCreateMainGroupDialog()"
                    style="padding: 10px 20px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                取消
            </button>
            <button type="button" onclick="createMainGroup()"
                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-save"></i> 创建主分组
            </button>
        </div>
    </div>
</div>

<!-- 成员编辑模态框 - 职责技能优先 -->
<div id="positionAndSkillsModal" class="position-modal" style="display: none;">
    <div class="position-modal-content">
        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">
            <i class="fas fa-user-cog"></i> 编辑成员信息
        </h3>

        <div style="margin-bottom: 15px; text-align: center;">
            <strong>成员：</strong><span id="positionAndSkillsMemberName" style="color: #007bff; font-size: 18px;"></span>
        </div>

        <!-- 主要功能区域：职责+技能 横向布局 -->
        <div style="display: grid; grid-template-columns: 350px 1fr; gap: 30px; margin-bottom: 30px;">

            <!-- 左侧：职责设置 -->
            <div style="background: #f8f9ff; padding: 15px; border-radius: 8px; border: 2px solid #e3f2fd;">
                <h4 style="margin: 0 0 15px 0; color: #1976d2; text-align: center; font-size: 16px;">
                    <i class="fas fa-crosshairs"></i> 设置职责
                </h4>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                    <div class="position-option" data-position="拆塔"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">🏗️</div>
                        <div style="font-weight: bold; font-size: 12px;">拆塔</div>
                    </div>
                    <div class="position-option" data-position="击杀"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">⚔️</div>
                        <div style="font-weight: bold; font-size: 12px;">击杀</div>
                    </div>
                    <div class="position-option" data-position="人伤"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">💥</div>
                        <div style="font-weight: bold; font-size: 12px;">人伤</div>
                    </div>
                    <div class="position-option" data-position="治疗"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">💚</div>
                        <div style="font-weight: bold; font-size: 12px;">治疗</div>
                    </div>
                    <div class="position-option" data-position="扛伤"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">🛡️</div>
                        <div style="font-weight: bold; font-size: 12px;">扛伤</div>
                    </div>
                    <div class="position-option" data-position="辅助"
                         style="padding: 8px; border: 2px solid #ddd; border-radius: 6px; text-align: center; cursor: pointer; transition: all 0.3s; background: white;">
                        <div style="font-size: 16px; margin-bottom: 3px;">🤝</div>
                        <div style="font-weight: bold; font-size: 12px;">辅助</div>
                    </div>
                </div>
            </div>

            <!-- 右侧：技能设置 -->
            <div style="background: #f0fff4; padding: 20px; border-radius: 10px; border: 2px solid #e8f5e8;">
                <h4 style="margin: 0 0 15px 0; color: #2e7d32; text-align: center; font-size: 18px;">
                    <i class="fas fa-cogs"></i> 设置技能
                </h4>

                <!-- 当前技能标签 -->
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; font-size: 14px;">当前技能：</label>
                    <div id="currentSkillsContainer" style="min-height: 45px; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background: white;">
                        <!-- 当前技能标签将在这里显示 -->
                    </div>
                </div>

                <!-- 技能预设 -->
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                        <label style="font-weight: bold; font-size: 14px;">技能预设：</label>
                        <div style="display: flex; gap: 8px;">
                            <button type="button" onclick="clearAllSkillPresets()"
                                    style="padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-trash"></i> 清空所有预设
                            </button>
                            <button type="button" onclick="togglePresetManagement()" id="presetManageBtn"
                                    style="padding: 4px 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-plus"></i> 添加预设
                            </button>
                        </div>
                    </div>
                    <div id="skillPresetsContainer" style="border: 1px solid #ddd; border-radius: 6px; padding: 15px; background: white; min-height: 120px;">
                        <!-- 技能预设将在这里显示 -->
                    </div>
                    <div style="font-size: 11px; color: #666; margin-top: 5px; text-align: center;">
                        💡 点击技能名称可选择/取消，点击右侧 ❌ 可删除单个预设
                    </div>
                    <!-- 预设管理区域 -->
                    <div id="presetManagementArea" style="display: none; margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e0e0e0;">
                        <div style="display: flex; gap: 8px; align-items: center;">
                            <input type="text" id="newPresetInput" placeholder="输入新预设技能名称"
                                   style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px;">
                            <button type="button" onclick="addSkillToPreset()"
                                    style="padding: 6px 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-plus"></i> 添加到预设
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 自定义技能输入 -->
                <div>
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; font-size: 14px;">自定义技能：</label>
                    <div style="display: flex; gap: 8px; align-items: center;">
                        <input type="text" id="customSkillInput" placeholder="输入技能名称"
                               style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                        <label style="display: flex; align-items: center; gap: 4px; font-size: 12px; white-space: nowrap;">
                            <input type="checkbox" id="addToPresetCheck" style="margin: 0;">
                            同时添加到预设
                        </label>
                        <button type="button" onclick="addCustomSkill()"
                                style="padding: 10px 20px; background: #2e7d32; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold;">
                            <i class="fas fa-plus"></i> 添加
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 次要功能：其他设置 -->
        <div style="background: #fafafa; padding: 15px; border-radius: 8px; border: 1px solid #e0e0e0; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <h5 style="margin: 0; color: #666; font-size: 16px;">
                    <i class="fas fa-cog"></i> 其他设置
                </h5>
                <button type="button" onclick="toggleOtherSettings()" id="toggleOtherBtn"
                        style="padding: 5px 10px; background: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    <i class="fas fa-chevron-down"></i> 展开
                </button>
            </div>

            <div id="otherSettingsContent" style="display: none; margin-top: 15px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; align-items: end;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px;">成员姓名：</label>
                        <input type="text" id="editMemberName"
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px;">职业：</label>
                        <select id="editMemberProfession"
                                style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                            <option value="素问">素问</option>
                            <option value="九灵">九灵</option>
                            <option value="潮光">潮光</option>
                            <option value="血河">血河</option>
                            <option value="神相">神相</option>
                            <option value="玄机">玄机</option>
                            <option value="铁衣">铁衣</option>
                            <option value="龙吟">龙吟</option>
                            <option value="碎梦">碎梦</option>
                            <option value="沧澜">沧澜</option>
                        </select>
                    </div>
                    <div>
                        <button onclick="confirmDeleteMember()"
                                style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                            <i class="fas fa-trash"></i> 删除成员
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; padding-top: 20px; border-top: 1px solid #ddd;">
            <button type="button" onclick="closePositionAndSkillsModal()"
                    style="padding: 12px 24px; margin-right: 15px; background: #6c757d; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" onclick="saveAllMemberInfo()"
                    style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                <i class="fas fa-save"></i> 保存设置
            </button>
        </div>
    </div>
</div>

<script>
// 技能管理相关变量
let currentEditingMemberSkills = [];
let currentEditingMemberForSkills = '';
let skillPresets = [];

// 保存请求队列
let saveQueue = [];
let isSaving = false;

// 技能分类函数
function getSkillCategory(skill) {
    // 攻击类技能
    const attackSkills = ['击杀', '九天', '三绝', '剑破', '点杀', '百步飞剑', '大蛇', '星火', '炉子', '岳飞', '花萦', '花海', '炮', '人伤击杀', '打架'];
    // 防守类技能
    const defenseSkills = ['保活', '无敌帧', '腾龙', '稳住', '沧溟', '猴棍', '老鹰', '资源', '统战'];
    // 辅助类技能
    const supportSkills = ['清泉', 'QTE', '纯肉辅', '新绝', '攻潮', '本家', '奶绝', '山盟', '辅绝', '萌冲'];
    // 控制类技能
    const controlSkills = ['冰墙', '太极', '铁壁', '小车', '降速', '约定', '约'];
    // 拆塔类技能
    const demolitionSkills = ['拆塔', '拆', '金木太剑', '猴棍拆'];

    const skillLower = skill.toLowerCase();

    if (attackSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'attack';
    if (defenseSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'defense';
    if (supportSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'support';
    if (controlSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'control';
    if (demolitionSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'demolition';

    return 'support'; // 默认分类
}

// 获取技能预设
async function loadSkillPresets() {
    try {
        const response = await fetch('/api/get_skill_presets');
        const data = await response.json();
        skillPresets = data.presets || [];
    } catch (error) {
        console.error('加载技能预设失败:', error);
        skillPresets = [];
    }
}

// 显示技能编辑模态框
function showSkillsEditModal(memberName) {
    currentEditingMemberForSkills = memberName;

    // 获取成员当前技能
    const memberCard = document.querySelector(`[data-member-name="${memberName}"]`);
    const skillsData = memberCard ? memberCard.dataset.skills : '';
    currentEditingMemberSkills = skillsData ? skillsData.split(',').filter(s => s.trim()) : [];

    // 设置成员名称
    document.getElementById('skillsEditMemberName').textContent = memberName;

    // 显示当前技能
    updateCurrentSkillsDisplay();

    // 显示技能预设
    updateSkillPresetsDisplay();

    // 显示模态框
    document.getElementById('skillsEditModal').style.display = 'flex';
}

// 更新当前技能显示
function updateCurrentSkillsDisplay() {
    const container = document.getElementById('currentSkillsContainer');

    if (currentEditingMemberSkills.length === 0) {
        container.innerHTML = '<span style="color: #6c757d; font-style: italic;">暂无技能标签</span>';
        return;
    }

    container.innerHTML = currentEditingMemberSkills.map(skill => `
        <span class="skill-tag-editable" style="
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        " onclick="removeSkill('${skill}')">
            ${skill} <i class="fas fa-times" style="margin-left: 4px;"></i>
        </span>
    `).join('');
}

// 更新技能预设显示
function updateSkillPresetsDisplay() {
    const container = document.getElementById('skillPresetsContainer');

    if (skillPresets.length === 0) {
        container.innerHTML = '<span style="color: #6c757d; font-style: italic;">暂无技能预设</span>';
        return;
    }

    container.innerHTML = skillPresets.map(skill => {
        const isSelected = currentEditingMemberSkills.includes(skill);
        return `
            <div style="
                display: inline-flex;
                align-items: center;
                background: ${isSelected ? '#28a745' : '#f8f9fa'};
                color: ${isSelected ? 'white' : '#333'};
                border: 1px solid ${isSelected ? '#28a745' : '#ddd'};
                margin: 2px;
                border-radius: 4px;
                font-size: 12px;
                overflow: hidden;
            ">
                <span style="
                    padding: 4px 8px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                " onclick="toggleSkillPreset('${skill}')">
                    <span>${skill}</span>
                    ${isSelected ? '<i class="fas fa-check"></i>' : ''}
                </span>
                <button style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 6px;
                    cursor: pointer;
                    font-size: 10px;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    border-left: 1px solid rgba(255,255,255,0.3);
                " onclick="deleteSkillPreset('${skill}')" title="删除预设: ${skill}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }).join('');
}

// 切换技能预设选择
function toggleSkillPreset(skill) {
    const index = currentEditingMemberSkills.indexOf(skill);
    if (index > -1) {
        currentEditingMemberSkills.splice(index, 1);
    } else {
        currentEditingMemberSkills.push(skill);
    }

    updateCurrentSkillsDisplay();
    updateSkillPresetsDisplay();
}

// 移除技能
function removeSkill(skill) {
    const index = currentEditingMemberSkills.indexOf(skill);
    if (index > -1) {
        currentEditingMemberSkills.splice(index, 1);
        updateCurrentSkillsDisplay();
        updateSkillPresetsDisplay();
    }
}

// 添加自定义技能
async function addCustomSkill() {
    const input = document.getElementById('customSkillInput');
    const addToPresetCheck = document.getElementById('addToPresetCheck');
    const skill = input.value.trim();

    if (!skill) {
        alert('请输入技能名称');
        return;
    }

    if (currentEditingMemberSkills.includes(skill)) {
        alert('该技能已存在');
        return;
    }

    // 添加到当前成员技能
    currentEditingMemberSkills.push(skill);

    // 如果选择了同时添加到预设
    if (addToPresetCheck.checked && !skillPresets.includes(skill)) {
        try {
            const response = await fetch('/api/add_skill_preset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ skill: skill })
            });

            const result = await response.json();

            if (result.success) {
                skillPresets = result.presets;
                showNotification('技能已添加到预设', 'success');
            } else {
                console.error('添加到预设失败:', result.error);
            }
        } catch (error) {
            console.error('添加到预设失败:', error);
        }
    }

    input.value = '';
    addToPresetCheck.checked = false;

    updateCurrentSkillsDisplay();
    updateSkillPresetsDisplay();
}

// 切换添加预设区域显示
function togglePresetManagement() {
    const area = document.getElementById('presetManagementArea');
    const btn = document.getElementById('presetManageBtn');

    if (area.style.display === 'none') {
        area.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-times"></i> 关闭';
        btn.style.background = '#dc3545';
    } else {
        area.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-plus"></i> 添加预设';
        btn.style.background = '#6c757d';
    }
}

// 添加技能到预设
async function addSkillToPreset() {
    const input = document.getElementById('newPresetInput');
    const skill = input.value.trim();

    if (!skill) {
        alert('请输入技能名称');
        return;
    }

    if (skillPresets.includes(skill)) {
        alert('该技能预设已存在');
        return;
    }

    try {
        const response = await fetch('/api/add_skill_preset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ skill: skill })
        });

        const result = await response.json();

        if (result.success) {
            skillPresets = result.presets;
            input.value = '';
            updateSkillPresetsDisplay();
            showNotification('技能预设添加成功', 'success');
        } else {
            alert('添加失败：' + result.error);
        }
    } catch (error) {
        console.error('添加技能预设失败:', error);
        alert('添加失败，请稍后重试');
    }
}

// 删除技能预设
async function deleteSkillPreset(skill) {
    if (!confirm(`确定要删除技能预设 "${skill}" 吗？`)) {
        return;
    }

    try {
        const response = await fetch('/api/delete_skill_preset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ skill: skill })
        });

        const result = await response.json();

        if (result.success) {
            skillPresets = result.presets;
            updateSkillPresetsDisplay();
            showNotification('技能预设删除成功', 'success');
        } else {
            alert('删除失败：' + result.error);
        }
    } catch (error) {
        console.error('删除技能预设失败:', error);
        alert('删除失败，请稍后重试');
    }
}

// 清空所有技能预设
async function clearAllSkillPresets() {
    if (!confirm('确定要清空所有技能预设吗？此操作不可撤销！')) {
        return;
    }

    try {
        const response = await fetch('/api/clear_skill_presets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            skillPresets = result.presets;
            updateSkillPresetsDisplay();
            showNotification('技能预设已清空', 'success');
        } else {
            alert('清空失败：' + result.error);
        }
    } catch (error) {
        console.error('清空技能预设失败:', error);
        alert('清空失败，请稍后重试');
    }
}

// 保存技能
async function saveSkills() {
    try {
        const response = await fetch('/api/update_member_skills', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                member_name: currentEditingMemberForSkills,
                skills: currentEditingMemberSkills
            })
        });

        const result = await response.json();

        if (result.success) {
            // 更新页面上的技能显示
            updateMemberSkillsDisplay(currentEditingMemberForSkills, currentEditingMemberSkills);
            closeSkillsEditModal();
            showNotification('技能标签保存成功', 'success');
        } else {
            alert('保存失败：' + result.error);
        }
    } catch (error) {
        console.error('保存技能失败:', error);
        alert('保存失败，请稍后重试');
    }
}

// 更新成员卡片的技能显示
function updateMemberSkillsDisplay(memberName, skills) {
    const memberCards = document.querySelectorAll(`[data-member-name="${memberName}"]`);

    memberCards.forEach(card => {
        // 更新data-skills属性
        card.dataset.skills = skills.join(',');

        // 更新技能容器
        let skillsContainer = card.querySelector('.skills-container');
        if (!skillsContainer) {
            skillsContainer = document.createElement('div');
            skillsContainer.className = 'skills-container';
            card.appendChild(skillsContainer);
        }

        skillsContainer.innerHTML = skills.map(skill => `
            <span class="skill-tag ${getSkillCategory(skill)}" title="${skill}">${skill}</span>
        `).join('');
    });
}

// 关闭技能编辑模态框
function closeSkillsEditModal() {
    document.getElementById('skillsEditModal').style.display = 'none';
    currentEditingMemberForSkills = '';
    currentEditingMemberSkills = [];
}

// 显示完整编辑模态框
function showCompleteEditModal(memberName, memberCard) {
    // 设置成员名称
    document.getElementById('positionAndSkillsMemberName').textContent = memberName;

    // 填充基本信息
    document.getElementById('editMemberName').value = memberName;
    document.getElementById('editMemberProfession').value = memberCard.dataset.profession || '素问';

    // 设置当前职责选择
    const currentPosition = memberCard.dataset.position || '拆塔';
    document.querySelectorAll('.position-option').forEach(option => {
        option.classList.remove('selected');
        if (option.dataset.position === currentPosition) {
            option.classList.add('selected');
            option.style.background = '#007bff';
            option.style.color = 'white';
            option.style.borderColor = '#007bff';
        } else {
            option.style.background = 'white';
            option.style.color = '#333';
            option.style.borderColor = '#ddd';
        }

        // 添加点击事件
        option.onclick = function() {
            document.querySelectorAll('.position-option').forEach(opt => {
                opt.classList.remove('selected');
                opt.style.background = 'white';
                opt.style.color = '#333';
                opt.style.borderColor = '#ddd';
            });
            this.classList.add('selected');
            this.style.background = '#007bff';
            this.style.color = 'white';
            this.style.borderColor = '#007bff';
        };
    });

    // 显示当前技能
    updateCurrentSkillsDisplay();

    // 显示技能预设
    updateSkillPresetsDisplay();

    // 确保其他设置区域是收起的
    document.getElementById('otherSettingsContent').style.display = 'none';
    document.getElementById('toggleOtherBtn').innerHTML = '<i class="fas fa-chevron-down"></i> 展开';

    // 显示模态框
    document.getElementById('positionAndSkillsModal').style.display = 'flex';
}



// 切换其他设置显示/隐藏
function toggleOtherSettings() {
    const content = document.getElementById('otherSettingsContent');
    const btn = document.getElementById('toggleOtherBtn');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-chevron-up"></i> 收起';
    } else {
        content.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-chevron-down"></i> 展开';
    }
}

// 保存所有成员信息
async function saveAllMemberInfo() {
    const newName = document.getElementById('editMemberName').value.trim();
    const newProfession = document.getElementById('editMemberProfession').value;
    const selectedPosition = document.querySelector('.position-option.selected');

    if (!newName) {
        alert('请输入成员姓名');
        return;
    }

    if (!selectedPosition) {
        alert('请选择职责');
        return;
    }

    const newPosition = selectedPosition.dataset.position;
    const originalName = currentEditingMemberForSkills;
    const memberSkills = [...currentEditingMemberSkills]; // 复制技能数组

    // 立即关闭模态框（但不清空变量），显示保存中状态
    document.getElementById('positionAndSkillsModal').style.display = 'none';

    // 添加到保存队列
    const saveTask = {
        newName,
        newProfession,
        newPosition,
        originalName,
        memberSkills
    };

    saveQueue.push(saveTask);
    showNotification(`保存请求已加入队列 (${saveQueue.length})`, 'info');

    // 处理队列
    processSaveQueue();
}

// 处理保存队列
async function processSaveQueue() {
    if (isSaving || saveQueue.length === 0) {
        return;
    }

    isSaving = true;

    while (saveQueue.length > 0) {
        const task = saveQueue.shift();
        showNotification(`正在保存 ${task.newName}... (剩余 ${saveQueue.length})`, 'info');

        try {
            await executeSaveTask(task);
            showNotification(`${task.newName} 保存成功`, 'success');
        } catch (error) {
            console.error('保存失败:', error);
            showNotification(`${task.newName} 保存失败`, 'error');
        }
    }

    isSaving = false;
}

// 执行单个保存任务
async function executeSaveTask(task) {
    const { newName, newProfession, newPosition, originalName, memberSkills } = task;

    try {
        let finalMemberName = newName; // 默认使用新名字

        // 1. 更新基本信息（名字、职业、职责）
        const basicResponse = await fetch('/update_member_info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                old_name: originalName,
                new_name: newName,
                profession: newProfession,
                position: newPosition
            })
        });

        const basicResult = await basicResponse.json();
        if (!basicResult.success) {
            throw new Error('保存基本信息失败：' + basicResult.error);
        }

        // 2. 保存技能
        const skillsResponse = await fetch('/api/update_member_skills', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                member_name: finalMemberName,
                skills: memberSkills
            })
        });

        const skillsResult = await skillsResponse.json();
        if (!skillsResult.success) {
            throw new Error('保存技能失败：' + skillsResult.error);
        }

        // 3. 更新页面显示
        if (newName !== originalName) {
            // 如果名字改变了，需要重新加载页面
            setTimeout(() => location.reload(), 500);
        } else {
            updateMemberAllDisplay(finalMemberName, newProfession, newPosition, memberSkills);
        }

    } catch (error) {
        throw error; // 重新抛出错误，让调用者处理
    } finally {
        // 清空编辑状态（无论成功还是失败）
        currentEditingMemberForSkills = '';
        currentEditingMemberSkills = [];
        currentEditingMember = '';
    }
}

// 确认删除成员
function confirmDeleteMember() {
    const memberName = currentEditingMemberForSkills;
    if (confirm(`确定要删除成员 "${memberName}" 吗？此操作不可撤销！`)) {
        deleteMemberDirect(memberName);
        closePositionAndSkillsModal();
    }
}

// 更新成员所有显示信息
function updateMemberAllDisplay(memberName, profession, position, skills) {
    const memberCards = document.querySelectorAll(`[data-member-name="${memberName}"]`);

    memberCards.forEach(card => {
        // 更新职业
        card.dataset.profession = profession;

        // 更新职责
        card.dataset.position = position;
        const positionBadge = card.querySelector('.position-badge');
        if (positionBadge) {
            positionBadge.textContent = position;
        }

        // 更新技能
        updateMemberSkillsDisplay(memberName, skills);

        // 更新职业颜色
        updateMemberCardProfessionColor(card, profession);
    });
}

// 更新成员卡片职业颜色
function updateMemberCardProfessionColor(card, profession) {
    const professionColors = {
        '素问': '#ff69b4',
        '九灵': '#9370db',
        '潮光': '#87ceeb',
        '血河': '#dc143c',
        '神相': '#4169e1',
        '玄机': '#ffd700',
        '铁衣': '#ff8c00',
        '龙吟': '#32cd32',
        '碎梦': '#2e8b57',
        '沧澜': '#20b2aa'
    };

    const color = professionColors[profession] || '#6c757d';
    card.style.borderLeftColor = color;
}

// 更新成员职责显示
function updateMemberPositionDisplay(memberName, newPosition) {
    const memberCards = document.querySelectorAll(`[data-member-name="${memberName}"]`);

    memberCards.forEach(card => {
        // 更新data-position属性
        card.dataset.position = newPosition;

        // 更新职责标识
        const positionBadge = card.querySelector('.position-badge');
        if (positionBadge) {
            positionBadge.textContent = newPosition;
        }
    });
}

// 关闭职责+技能编辑模态框
function closePositionAndSkillsModal() {
    document.getElementById('positionAndSkillsModal').style.display = 'none';
    currentEditingMemberForSkills = '';
    currentEditingMemberSkills = [];
    currentEditingMember = '';
}

// 为现有的技能标签应用分类样式
function applySkillCategoriesToExistingTags() {
    const skillTags = document.querySelectorAll('.skill-tag');
    skillTags.forEach(tag => {
        const skillText = tag.textContent.trim();
        const category = getSkillCategory(skillText);
        tag.className = 'skill-tag ' + category;
        tag.title = skillText; // 添加完整技能名称的提示
    });
}

// 页面加载时初始化技能预设
document.addEventListener('DOMContentLoaded', function() {
    loadSkillPresets();

    // 为现有技能标签应用分类样式
    applySkillCategoriesToExistingTags();
});
</script>

{% endblock %}
